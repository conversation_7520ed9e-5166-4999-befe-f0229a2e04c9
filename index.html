<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>章节详情</title>
    <link href="css/style.css" type="text/css" rel="stylesheet" />
</head>

<body>
    <nav id="breadcrumb-nav" class="breadcrumb-nav"></nav>
    <header id="page-header" class="page-header"></header>
    <div id="content" onclick="handleContentClick(event)"></div>
    <footer id="page-footer">
        <button class="search-trigger" id="search-trigger">
            🔍
        </button>
        <button class="index-manage" id="index-manage" onclick="showIndexManagement()" title="搜索索引管理">
            ⚙️
        </button>
        <button class="reading-settings" id="reading-settings" onclick="toggleSettings()" title="阅读设置">
            📖
        </button>
        <button class="totop" onclick="window.scrollTo({top: 0, behavior: 'smooth'});">
            &#8673;
        </button>
    </footer>

    <div id="floating-search-container" class="floating-search-container">
        <div class="floating-search-box">
            <div class="search-input-wrapper">
                <input type="text" id="search-input" placeholder="搜索内容..." />
                <button id="clear-search-button" class="clear-search-button" title="清除搜索内容">✕</button>
            </div>
            <div class="floating-search-buttons">
                <button id="search-button">搜索</button>
                <button id="close-search-button">取消</button>
            </div>
        </div>
    </div>

    <div id="floating-results-container" class="floating-results-container">
        <div id="search-results" class="search-results floating"></div>
    </div>

    <!-- 脚注弹窗 -->
    <dialog id="footnote-dialog" class="footnote-dialog">
        <div class="footnote-dialog-header">
            <h4 id="footnote-title">脚注</h4>
            <button class="footnote-close-btn" onclick="closeFootnoteDialog()">×</button>
        </div>
        <div class="footnote-dialog-content" id="footnote-content">
            <!-- 脚注内容将在这里显示 -->
        </div>
    </dialog>

    <!-- 设置面板 -->
    <div id="settings-panel" class="settings-panel">
        <div class="settings-content">
            <div class="settings-header">
                <h3>阅读设置</h3>
                <button class="settings-close" onclick="toggleSettings()">×</button>
            </div>

            <div class="settings-section">
                <h4>背景颜色</h4>
                <div class="color-options">
                    <div class="color-option" data-bg="default" style="background: #ffffff; border: 2px solid #ddd;">
                        <span>默认</span>
                    </div>
                    <div class="color-option" data-bg="warm" style="background: #fdf6e3; border: 2px solid #ddd;">
                        <span>护眼</span>
                    </div>
                    <div class="color-option" data-bg="dark"
                        style="background: #2b2b2b; color: white; border: 2px solid #555;">
                        <span>深色</span>
                    </div>
                    <div class="color-option" data-bg="green" style="background: #f0f8f0; border: 2px solid #ddd;">
                        <span>绿色</span>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h4>字体设置</h4>
                <div class="font-options">
                    <label>字体大小：</label>
                    <input type="range" id="font-size-slider" min="16" max="30" value="20" step="1">
                    <span id="font-size-value">16px</span>
                </div>
                <div class="font-family-options">
                    <label>字体类型：</label>
                    <select id="font-family-select">
                        <option value="wenkai">霞鹜文楷（默认）</option>
                        <option value="fangzhengkaiti">方正楷体</option>
                        <option value="jinkai">仓耳今楷</option>
                        <option value="songkai">仓耳宋楷</option>
                        <option value="pingxianzhensong">屏显臻宋</option>
                        <option value="huosong">寒蝉活宋体</option>
                        <option value="xiaobiaosong">方正小标宋</option>
                        <option value="huaxin">仓耳华新体</option>
                        <option value="huohei">寒蝉活黑体</option>
                        <option value="pingyindingkai">拼音鼎楷</option>
                    </select>
                </div>
                <div class="line-height-options">
                    <label>行间距：</label>
                    <input type="range" id="line-height-slider" min="1.2" max="2.5" value="1.6" step="0.1">
                    <span id="line-height-value">1.6</span>
                </div>
            </div>

            <div class="settings-section page-settings">
                <h4>页面设置</h4>
                <div class="width-options">
                    <label>内容宽度：</label>
                    <input type="range" id="content-width-slider" min="600" max="1200" value="1200" step="50">
                    <span id="content-width-value">800px</span>
                </div>
            </div>

            <div class="settings-actions">
                <button onclick="resetSettings()" class="reset-btn">恢复默认</button>
                <button onclick="toggleSettings()" class="close-btn">关闭</button>
            </div>
        </div>
    </div>

    <script src="js/marked.js"></script>
    <script>
        let currentChapter = "bob";
        const BOOKS_DIR = "./books/";
        let searchIndex = []; // 存储所有可搜索内容的索引
        let bodyScrollDisabled = false; // 跟踪滚动状态
        let scrollY; // 保存滚动位置
        let indexLoaded = false; // 标记索引是否已加载

        // 设置相关变量
        let settingsVisible = false;
        const defaultSettings = {
            backgroundColor: 'default',
            fontSize: 16,
            fontFamily: 'wenkai',
            lineHeight: 1.6,
            contentWidth: 800
        };
        let currentSettings = { ...defaultSettings };

        const renderer = new marked.Renderer();
        renderer.heading = function ({ text, depth }) {
            if (depth === 1) {
                return ''; // 不生成一级标题的 HTML
            }
            return `<h${depth} id="${text}">
            <a href="#${text}-${depth}">${text}</a>
        </h${depth}>`;
        };

        renderer.link = function (token) {
            const href = token.href;
            const title = token.title;
            const text = token.text;
            return `<a href="${href}" title="${title || ''}" target="_blank">${text}</a>`;
        };

        marked.use({ renderer });

        // 更新页面标题
        function updatePageHeader(chapterName, subDir = "") {
            const headerDiv = document.getElementById("page-header");
            if (!headerDiv) return;

            // 根据页面层级设置不同标题
            if (chapterName === "bob") {
                // 第一级页面 - 书架
                headerDiv.style.display = 'block';
                headerDiv.innerHTML = '<div class="header-title">我的书架</div>';
            } else if (chapterName === "toc") {
                // 第二级页面 - 书籍目录
                // 获取最后一个目录名作为当前目录名
                const dirParts = subDir.split('/').filter(part => part); // 过滤掉空字符串
                const currentDirName = dirParts[dirParts.length - 1] || '书籍目录';
                headerDiv.style.display = 'block';
                headerDiv.innerHTML = `<div class="header-title">${currentDirName}</div>`;
            } else {
                // 第三级页面 - 章节内容
                // 从当前章节内容中获取标题

                const contentDiv = document.getElementById("content");
                if (contentDiv) {
                    const firstHeading = contentDiv.querySelector('h1, h2, h3, h4, h5, h6');
                    const title = firstHeading ? firstHeading.textContent : '章节内容';
                    headerDiv.style.display = 'none';
                    headerDiv.innerHTML = `<div class="header-title">${title}</div>`;
                } else {
                    headerDiv.innerHTML = '<div class="header-title">章节内容</div>';
                }
            }
        }

        // 获取章节显示名称
        function getChapterDisplayName(chapterName) {
            const chapterNameMap = {
                'gm': '纲目',
                'toc': '目录',
                'bob': '书架'
            };
            return chapterNameMap[chapterName] || `第${chapterName}章`;
        }

        // 更新面包屑导航
        function updateBreadcrumb(chapterName, subDir = "", chapterTitle = "") {
            const breadcrumbNav = document.getElementById("breadcrumb-nav");
            if (!breadcrumbNav) return;

            let breadcrumbHtml = '';

            if (chapterName === "bob") {
                // 书架页面 - 不显示面包屑
                breadcrumbNav.classList.remove('show');
                return;
            } else if (chapterName === "toc") {
                // 目录页面
                const dirParts = subDir.split('/').filter(part => part);

                // 添加书架链接
                breadcrumbHtml += '<a href="#" onclick="loadChapter(\'bob\', \'\')">首页</a>';

                // 添加所有层级的目录链接
                for (let i = 0; i < dirParts.length; i++) {
                    const partialPath = dirParts.slice(0, i + 1).join('/');
                    const dirName = dirParts[i];

                    if (i === dirParts.length - 1) {
                        // 最后一级，显示为当前项
                        breadcrumbHtml += `<span class="separator"> / </span><span class="current" title="${dirName}">${dirName}</span>`;
                    } else {
                        // 中间层级，显示为可点击链接
                        breadcrumbHtml += `<span class="separator"> / </span><a href="#" onclick="loadSubDir('${partialPath}')">${dirName}</a>`;
                    }
                }
            } else {
                // 章节页面
                const dirParts = subDir.split('/').filter(part => part);

                // 添加书架链接
                breadcrumbHtml += '<a href="#" onclick="loadChapter(\'bob\', \'\')">首页</a>';

                // 添加所有层级的目录链接
                for (let i = 0; i < dirParts.length; i++) {
                    const partialPath = dirParts.slice(0, i + 1).join('/');
                    const dirName = dirParts[i];

                    // 所有目录层级都显示为可点击链接
                    breadcrumbHtml += `<span class="separator"> / </span><a href="#" onclick="loadSubDir('${partialPath}')">${dirName}</a>`;
                }

                // 添加当前章节
                const displayTitle = chapterTitle || getChapterDisplayName(chapterName);
                breadcrumbHtml += `<span class="separator"> / </span><span class="current" title="${displayTitle}">${displayTitle}</span>`;
            }

            breadcrumbNav.innerHTML = breadcrumbHtml;
            breadcrumbNav.classList.add('show');

            // 移动端面包屑优化
            optimizeMobileBreadcrumb();
        }

        async function loadChapter(chapterName, subDir = "", searchTerm = "", anchorId = "", lineNumber = 0, footnoteKey = "", sectionNumber = "") {
            const contentDiv = document.getElementById("content");
            if (!contentDiv) return;

            // 页面跳转时重置滚动位置到顶部（除非有特定的锚点定位需求）
            if (!anchorId && !searchTerm) {
                window.scrollTo(0, 0);
            }

            // 检查是否有子目录
            currentChapter = chapterName;

            updateURL(chapterName, subDir, searchTerm, anchorId, lineNumber);
            // 先更新基本标题
            updatePageHeader(chapterName, subDir);
            // 更新面包屑导航（初始更新，章节标题稍后获取）
            updateBreadcrumb(chapterName, subDir);

            try {
                contentDiv.innerHTML = "加载中...";
                // 根据页面类型设置标题
                if (chapterName === 'bob') {
                    document.title = '我的书架';
                } else if (chapterName === 'toc') {
                    // 从 subDir 中提取书籍名称
                    const bookName = subDir.split('/')[0];
                    document.title = bookName || '书籍目录';
                } else {
                    // 获取当前章节的标题
                    const normalizedSubDir = subDir ? (subDir.endsWith('/') ? subDir : subDir + '/') : '';
                    const url = `${BOOKS_DIR}${normalizedSubDir}${chapterName}.md`;

                    try {
                        const response = await fetch(url);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        const text = await response.text();
                        const tokens = marked.lexer(text);
                        // 获取第一个标题作为章节标题
                        const firstHeading = tokens.find(token => token.type === 'heading');
                        document.title = firstHeading ? firstHeading.text : getChapterDisplayName(chapterName);
                    } catch (error) {
                        // 如果获取文件失败，使用默认的章节显示名称
                        document.title = getChapterDisplayName(chapterName);
                    }
                }

                // 确保subDir以/结尾
                const normalizedSubDir = subDir ? (subDir.endsWith('/') ? subDir : subDir + '/') : '';
                const url = `${BOOKS_DIR}${normalizedSubDir}${chapterName}.md`;
                console.log('请求的 URL:', url);

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const markdown = await response.text();

                try {
                    if (!window.marked) {
                        throw new Error('marked.js 未正确加载');
                    }

                    // 段落级别脚注处理函数
                    function processFootnotes(markdownText) {
                        // 按空行分割成块
                        const blocks = markdownText.split(/\n\s*\n/);
                        const processedBlocks = [];
                        const allFootnotes = {}; // 收集所有脚注信息
                        let globalFootnoteId = 1;

                        let i = 0;
                        while (i < blocks.length) {
                            const block = blocks[i].trim();

                            if (block === '') {
                                processedBlocks.push('');
                                i++;
                                continue;
                            }

                            // 检查是否包含脚注引用
                            const footnoteRefs = block.match(/\[\^([^\]]+)\]/g) || [];

                            if (footnoteRefs.length > 0) {
                                console.log(`\n=== 处理包含脚注的段落 ${Math.floor(i / 2) + 1} ===`);
                                console.log('段落内容:', block.substring(0, 100) + '...');
                                console.log('脚注引用:', footnoteRefs);

                                // 这是包含脚注的段落，收集紧跟的脚注定义
                                let processedParagraph = block;
                                const paragraphFootnotes = {};
                                let j = i + 1;

                                // 收集紧跟的脚注定义
                                while (j < blocks.length) {
                                    const nextBlock = blocks[j].trim();
                                    if (nextBlock === '') {
                                        j++;
                                        continue;
                                    }

                                    // 检查是否为脚注定义块
                                    const footnoteLines = nextBlock.split('\n');
                                    let isFootnoteBlock = true;

                                    for (const line of footnoteLines) {
                                        if (line.trim() === '') continue;
                                        if (!line.match(/^\[\^([^\]]+)\]:/)) {
                                            isFootnoteBlock = false;
                                            break;
                                        }
                                    }

                                    if (isFootnoteBlock) {
                                        // 解析这个脚注定义块
                                        console.log(`收集脚注定义块 ${j}:`, nextBlock.substring(0, 100) + '...');

                                        for (const line of footnoteLines) {
                                            if (line.trim() === '') continue;
                                            const footnoteDefMatch = line.match(/^\[\^([^\]]+)\]:\s*(.*)$/);
                                            if (footnoteDefMatch) {
                                                const key = footnoteDefMatch[1];
                                                const content = footnoteDefMatch[2];
                                                paragraphFootnotes[key] = content;

                                                // 提取节号信息
                                                const sectionMatch = processedParagraph.match(/^(\d+[上中下]?)\s+/);
                                                const sectionNumber = sectionMatch ? sectionMatch[1] : null;

                                                // 添加到全局脚注集合
                                                allFootnotes[key] = {
                                                    content: content,
                                                    sectionNumber: sectionNumber
                                                };

                                                console.log(`  收集到: [^${key}] = "${content.substring(0, 30)}..." (节号: ${sectionNumber})`);
                                            }
                                        }
                                        j++;
                                    } else {
                                        // 遇到非脚注定义的内容，停止收集
                                        break;
                                    }
                                }

                                console.log('该段落的脚注映射:', Object.keys(paragraphFootnotes));

                                // 处理这个段落的脚注
                                const footnoteKeys = [...new Set(footnoteRefs.map(ref => ref.match(/\[\^([^\]]+)\]/)[1]))];

                                // 分析脚注模式
                                const isNumericPattern = footnoteKeys.every(key => /^\d+$/.test(key));
                                const isAlphaPattern = footnoteKeys.every(key => /^[a-zA-Z]$/.test(key));

                                // 提取段落开头的数字和可能的上中下后缀（节号）
                                const sectionMatch = processedParagraph.match(/^(\d+[上中下]?)\s+/);
                                const sectionNumber = sectionMatch ? sectionMatch[1] : null;
                                console.log('节号:', sectionNumber);

                                if (isNumericPattern || isAlphaPattern) {
                                    // 自动重新编号模式
                                    const sortedKeys = isNumericPattern
                                        ? footnoteKeys.sort((a, b) => parseInt(a) - parseInt(b))
                                        : footnoteKeys.sort();

                                    // 为每个脚注分配新的显示编号（每段落重新开始）
                                    const keyMapping = {};
                                    sortedKeys.forEach((originalKey, index) => {
                                        if (isNumericPattern) {
                                            keyMapping[originalKey] = (index + 1).toString();
                                        } else {
                                            keyMapping[originalKey] = String.fromCharCode(97 + index); // a, b, c...
                                        }
                                    });

                                    console.log('编号映射:', keyMapping);

                                    // 替换脚注引用
                                    Object.entries(keyMapping).forEach(([originalKey, displayKey]) => {
                                        if (paragraphFootnotes[originalKey]) {
                                            const content = paragraphFootnotes[originalKey];
                                            const globalId = globalFootnoteId++;
                                            const regex = new RegExp(`\\[\\^${originalKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\]`, 'g');

                                            processedParagraph = processedParagraph.replace(regex,
                                                `<sup><a href="#" id="fnref${globalId}" class="footnote-ref" data-footnote-content="${content.replace(/"/g, '&quot;')}" data-footnote-key="${displayKey}" data-paragraph-index="${Math.floor(i / 2) + 1}" data-section-number="${sectionNumber || ''}" onclick="showFootnoteDialog(event, this)">${displayKey}</a></sup>`
                                            );
                                            console.log(`  替换 [^${originalKey}] → ${displayKey}`);
                                        } else {
                                            console.log(`  ❌ 未找到脚注内容: [^${originalKey}]`);
                                        }
                                    });
                                } else {
                                    // 保持原始命名模式
                                    footnoteKeys.forEach(originalKey => {
                                        if (paragraphFootnotes[originalKey]) {
                                            const content = paragraphFootnotes[originalKey];
                                            const globalId = globalFootnoteId++;
                                            const regex = new RegExp(`\\[\\^${originalKey.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\]`, 'g');

                                            processedParagraph = processedParagraph.replace(regex,
                                                `<sup><a href="#" id="fnref${globalId}" class="footnote-ref" data-footnote-content="${content.replace(/"/g, '&quot;')}" data-footnote-key="${originalKey}" data-paragraph-index="${Math.floor(i / 2) + 1}" data-section-number="${sectionNumber || ''}" onclick="showFootnoteDialog(event, this)">${originalKey}</a></sup>`
                                            );
                                            console.log(`  替换 [^${originalKey}] → ${originalKey}`);
                                        } else {
                                            console.log(`  ❌ 未找到脚注内容: [^${originalKey}]`);
                                        }
                                    });
                                }

                                processedBlocks.push(processedParagraph);
                                i = j; // 跳过已处理的脚注定义块
                            } else {
                                // 普通段落，直接添加
                                processedBlocks.push(block);
                                i++;
                            }
                        }

                        return {
                            markdownText: processedBlocks.join('\n\n'),
                            footnotes: allFootnotes
                        };
                    }



                    // 生成脚注HTML（现在默认不显示，保留函数以备将来使用）
                    function generateFootnotesHTML(footnotes, showFootnotes = false) {
                        // 现在脚注只在弹窗中显示，页面底部默认不显示
                        return '';
                    }

                    // 处理脚注
                    const { markdownText: processedMarkdown, footnotes } = processFootnotes(markdown);
                    console.log(`处理了 ${Object.keys(footnotes).length} 个脚注`);

                    const tokens = marked.lexer(processedMarkdown);
                    const tocHtml = generateTOC(tokens);

                    // 使用marked的扩展功能来处理图片路径
                    const imageExtension = {
                        name: 'image',
                        renderer(token) {
                            let href = token.href;
                            const title = token.title;
                            const text = token.text;

                            // // 处理相对路径
                            // if (!href.startsWith('http') && !href.startsWith('/') && !href.startsWith('data:')) {
                            //   // 构建正确的图片路径
                            //   const basePath = subDir ? `./books/${subDir}/` : './books/';
                            //   href = basePath + href;
                            // }

                            // 修复图片路径处理逻辑
                            const relPrefixes = ['http', 'https', 'data:', '//', '/', '#'];
                            if (!relPrefixes.some(prefix => href.startsWith(prefix))) {
                                const basePath = subDir ? `books/${subDir}` : 'books';
                                if (href.startsWith('./')) {
                                    // 相对路径：./img/da1.png -> books/诗歌合辑/大本诗歌/img/da1.png
                                    href = `${basePath}${href.substring(1)}`;
                                } else {
                                    // 直接路径：img/da1.png -> books/诗歌合辑/大本诗歌/img/da1.png
                                    href = `${basePath}/${href}`;
                                }
                            }

                            // 生成图片HTML，添加错误处理
                            let html = `<img src="${href}" alt="${text || ''}"`;

                            if (title) {
                                html += ` title="${title}"`;
                            }

                            // 添加样式和错误处理
                            html += ` style="max-width: 100%; height: auto;" loading="lazy"`;
                            html += ` onerror="this.style.display='none'; console.error('图片加载失败:', this.src);"`;
                            html += `>`;

                            return html;
                        }
                    };

                    // 临时使用扩展
                    marked.use({ extensions: [imageExtension] });

                    // 解析内容
                    let contentHtml = marked.parse(processedMarkdown);

                    // 脚注只在弹窗中显示，不在页面底部显示

                    // 重置marked配置（避免影响其他地方）
                    marked.use({ extensions: [] });

                    // 提取一级标题内容
                    let mainTitle = '';
                    tokens.some((token) => {
                        if (token.type === "heading" && token.depth === 1) {
                            mainTitle = `<h1 id="${token.text}">${token.text}</h1>`;
                            return true;
                        }
                        return false;
                    });

                    // 定义导航栏，首页和二级目录不显示
                    let navButtons = '';
                    // 检查是否有子目录
                    console.log('chapterName:', chapterName !== "bob");
                    console.log('subDir:', subDir);
                    if (chapterName !== "bob" && chapterName !== "toc") {
                        navButtons = `
            <div class="navigation">
              <a href="#" onclick="prevChapter('${subDir}')">&#11013;往前</a><a href="#" onclick="nextChapter('${subDir}')">往后&#10145;</a>
            </div>
          `;
                        // 添加调试日志
                        console.log('navButtons已生成:', navButtons);
                    } else {
                        // 目录页面和书架页面不显示导航按钮
                        navButtons = '';
                        console.log('不显示导航按钮');
                    }

                    // 只在非bob和非toc页面显示目录
                    const tocDiv = (chapterName !== "bob" && chapterName !== "toc") ? `<div class="toc">${tocHtml}</div>` : '';

                    // 根据页面类型决定是否使用markdown-content包装
                    const content = (chapterName === "bob" || chapterName === "toc") ?
                        contentHtml.replace(/<\/?p>/g, '') : // 移除p标签
                        `<div class="markdown-content">${contentHtml}</div>`;

                    contentDiv.innerHTML = `
          ${mainTitle}
          ${navButtons}
          ${tocDiv}
          ${content}
        `;
                    // 添加bob页面标识类
                    if (chapterName === "bob") {
                        contentDiv.classList.add("bob-page");
                    } else {
                        contentDiv.classList.remove("bob-page");
                    }
                    // 内容加载完成后，再次更新标题以显示具体章节名
                    if (chapterName !== "bob" && chapterName !== "toc") {
                        updatePageHeader(chapterName, subDir);
                        // 获取实际的章节标题并更新面包屑
                        const firstHeading = contentDiv.querySelector('h1, h2, h3, h4, h5, h6');
                        const actualTitle = firstHeading ? firstHeading.textContent : getChapterDisplayName(chapterName);
                        updateBreadcrumb(chapterName, subDir, actualTitle);
                    }

                    // 如果是脚注匹配，直接打开脚注弹窗，不进行内容高亮
                    if (footnoteKey && searchTerm) {
                        console.log(`检测到脚注匹配，准备自动打开脚注弹窗: 脚注键="${footnoteKey}", 搜索词="${searchTerm}", 节号="${sectionNumber}"`);
                        setTimeout(() => {
                            autoOpenFootnotePopup(footnoteKey, searchTerm, sectionNumber);
                        }, 500); // 延迟500ms确保页面渲染完成
                    } else if (searchTerm) {
                        // 只有非脚注匹配时才进行内容高亮
                        highlightContentSearchTerm(searchTerm, anchorId, lineNumber);
                    }
                } catch (parseError) {
                    console.error("Markdown解析失败:", parseError);
                    contentDiv.innerHTML = "内容解析失败";
                }
            } catch (error) {
                console.error("加载章节失败:", error);
                contentDiv.innerHTML = `加载失败: ${error.message}`;
            }
        }
        // 生成目录HTML
        function generateTOC(tokens) {
            let toc = '';  // 移除重复的目录标题
            let currentDepth = 1;

            tokens.forEach((token) => {
                if (token.type === "heading" && token.depth !== 1) {
                    if (token.depth > currentDepth) {
                        // 如果当前标题深度大于上一个标题深度，嵌套进子列表
                        toc += `<ul class="toc-list">`;
                    } else if (token.depth < currentDepth) {
                        // 如果当前标题深度小于上一个标题深度，关闭子列表
                        for (let i = 0; i < currentDepth - token.depth; i++) {
                            toc += "</ul>";
                        }
                    }
                    // 生成目录项
                    toc += `
                  <li class="toc-item" id="${token.text}-${token.depth}">
                      <a href="#${token.text}">${token.text}</a>
                  </li>
              `;
                    currentDepth = token.depth;
                }
            });

            // 关闭所有未关闭的ul标签
            for (let i = 1; i < currentDepth; i++) {
                toc += "</ul>";
            }

            toc += "</ul>";
            return toc;
        }

        // 添加更新URL函数
        function updateURL(chapterName, subDir = '', searchTerm = '', anchorId = '', lineNumber = 0) {
            // 规范化 subDir：移除开头和结尾的斜杠
            subDir = subDir.replace(/^\/+|\/+$/g, '');
            const baseUrl = window.location.pathname;
            let newUrl = `${baseUrl}?chapter=${chapterName}${subDir ? `&dir=${subDir}` : ''}`;

            // 如果有搜索词，添加到URL参数中
            if (searchTerm) {
                newUrl += `&search=${encodeURIComponent(searchTerm)}`;

                // 如果有锚点ID和行号，也添加到URL参数中
                if (anchorId) {
                    newUrl += `&anchor=${encodeURIComponent(anchorId)}`;
                }

                if (lineNumber > 0) {
                    newUrl += `&line=${lineNumber}`;
                }
            }

            window.history.pushState({ chapterName, subDir, searchTerm, anchorId, lineNumber }, '', newUrl);

            // 添加调试日志
            console.log('URL已更新:', newUrl);
        }

        // 添加popstate事件监听
        window.addEventListener('popstate', (event) => {
            if (event.state) {
                const { chapterName, subDir, searchTerm, anchorId, lineNumber } = event.state;
                loadChapter(chapterName, subDir, searchTerm, anchorId, lineNumber);
            }
        });

        async function init() {
            // 预先加载bob.md中的分类信息
            try {
                window.bobCategories = await getCategoriesFromBob();
                console.log('预加载分类完成:', window.bobCategories);
            } catch (error) {
                console.error('预加载分类失败:', error);
                window.bobCategories = ['圣经研读', '生命读经', '属灵书报', '倪著文集', '李著文集', '特会纲目', '诗歌合辑'];
            }

            // 从URL参数中获取初始状态
            const params = new URLSearchParams(window.location.search);
            const chapter = params.get('chapter') || 'bob';  // 默认值改为 'bob'
            const dir = params.get('dir') || '';
            const search = params.get('search') || '';
            const anchor = params.get('anchor') || '';
            const line = parseInt(params.get('line') || '0', 10);

            console.log('初始化参数:', { chapter, dir, search, anchor, line });

            // 使用URL参数加载正确的章节，并传递搜索词参数
            await loadChapter(chapter, dir, search, anchor, line);

            // 初始化搜索功能
            initSearch();
        }

        // 初始化搜索功能
        function initSearch() {
            console.log('初始化搜索功能...');

            // 绑定搜索触发按钮
            const searchTrigger = document.getElementById('search-trigger');
            const floatingSearchContainer = document.getElementById('floating-search-container');
            const floatingResultsContainer = document.getElementById('floating-results-container');
            const searchInput = document.getElementById('search-input');
            const closeSearchButton = document.getElementById('close-search-button');
            const clearSearchButton = document.getElementById('clear-search-button');



            // 添加滚动阻止事件监听器
            let lastY;
            document.addEventListener('touchstart', function (e) {
                if (document.documentElement.classList.contains('modal-open')) {
                    const target = e.target;
                    // 允许弹窗内容区域滚动
                    if (target.closest('.floating-search-box') ||
                        target.closest('.search-results') ||
                        target.closest('.results-content') ||
                        target.closest('.footnote-dialog-content') ||
                        target.closest('.footnote-dialog') ||
                        target.closest('.footnote-popup-content') ||
                        target.closest('.footnote-popup') ||
                        target.closest('.chapter-modal-content') ||
                        target.closest('.modal-chapter-content')) {
                        return;
                    }
                    lastY = e.touches[0].clientY;
                }
            }, { passive: false });

            document.addEventListener('touchmove', function (e) {
                if (document.documentElement.classList.contains('modal-open')) {
                    const target = e.target;
                    // 允许弹窗内容区域滚动
                    if (target.closest('.floating-search-box') ||
                        target.closest('.search-results') ||
                        target.closest('.results-content') ||
                        target.closest('.footnote-dialog-content') ||
                        target.closest('.footnote-dialog') ||
                        target.closest('.footnote-popup-content') ||
                        target.closest('.footnote-popup') ||
                        target.closest('.chapter-modal-content') ||
                        target.closest('.modal-chapter-content')) {
                        return;
                    }
                    e.preventDefault();
                }
            }, { passive: false });

            // 为桌面浏览器添加滚动阻止
            window.addEventListener('wheel', function (e) {
                if (document.documentElement.classList.contains('modal-open')) {
                    const target = e.target;
                    // 允许弹窗内容区域滚动
                    if (target.closest('.floating-search-box') ||
                        target.closest('.search-results') ||
                        target.closest('.results-content') ||
                        target.closest('.footnote-dialog-content') ||
                        target.closest('.footnote-dialog') ||
                        target.closest('.footnote-popup-content') ||
                        target.closest('.footnote-popup') ||
                        target.closest('.chapter-modal-content') ||
                        target.closest('.modal-chapter-content')) {
                        return;
                    }
                    e.preventDefault();
                }
            }, { passive: false });

            // 点击搜索按钮显示搜索框
            searchTrigger.addEventListener('click', function () {
                floatingSearchContainer.classList.add('active');
                document.documentElement.classList.add('modal-open');
                searchInput.focus(); // 自动聚焦到输入框

                // 检查是否有内容来决定是否显示清除按钮
                const hasContent = searchInput.value.trim().length > 0;
                if (hasContent) {
                    clearSearchButton.classList.add('visible');
                } else {
                    clearSearchButton.classList.remove('visible');
                }
            });

            // 点击关闭按钮隐藏搜索框
            closeSearchButton.addEventListener('click', function () {
                floatingSearchContainer.classList.remove('active');
                if (!floatingResultsContainer.classList.contains('active')) {
                    document.documentElement.classList.remove('modal-open');
                }
            });

            // 点击搜索容器外部区域关闭搜索框
            floatingSearchContainer.addEventListener('click', function (event) {
                // 如果点击的是搜索容器本身（而非其子元素）
                if (event.target === floatingSearchContainer) {
                    floatingSearchContainer.classList.remove('active');
                    if (!floatingResultsContainer.classList.contains('active')) {
                        document.documentElement.classList.remove('modal-open');
                    }
                }
            });

            // 点击搜索结果容器外部区域关闭搜索结果
            floatingResultsContainer.addEventListener('click', function (event) {
                // 如果点击的是结果容器本身（而非其子元素）
                if (event.target === floatingResultsContainer) {
                    floatingResultsContainer.classList.remove('active');
                    if (!floatingSearchContainer.classList.contains('active')) {
                        document.documentElement.classList.remove('modal-open');
                    }
                }
            });

            // 绑定Enter键触发搜索
            searchInput.addEventListener('keypress', function (event) {
                if (event.key === 'Enter') {
                    performSearch();
                }
            });

            // 监听输入框内容变化，控制清除按钮显示
            searchInput.addEventListener('input', function () {
                const hasContent = searchInput.value.trim().length > 0;
                if (hasContent) {
                    clearSearchButton.classList.add('visible');
                } else {
                    clearSearchButton.classList.remove('visible');
                }
            });

            // 绑定清除按钮点击事件
            clearSearchButton.addEventListener('click', function () {
                searchInput.value = '';
                clearSearchButton.classList.remove('visible');
                searchInput.focus(); // 清除后重新聚焦到输入框

                // 清除搜索结果
                const searchResults = document.getElementById('search-results');
                if (searchResults) {
                    searchResults.innerHTML = '';
                }

                // 隐藏搜索结果容器
                floatingResultsContainer.classList.remove('active');
                if (!floatingSearchContainer.classList.contains('active')) {
                    document.documentElement.classList.remove('modal-open');
                }
            });

            // 绑定搜索按钮点击事件
            document.getElementById('search-button').addEventListener('click', performSearch);

            console.log('搜索功能初始化完成');
        }

        async function loadSubDir(subDir) {
            currentChapter = "toc";
            await loadChapter(currentChapter, subDir + "/");
        }

        async function nextChapter(subDir) {
            try {
                const chapters = await getChapterList(subDir);
                console.log("nextChapter - 当前章节:", currentChapter);
                console.log("nextChapter - 目录列表:", chapters);

                // 查找当前章节在列表中的索引
                let currentIndex = -1;
                for (let i = 0; i < chapters.length; i++) {
                    if (chapters[i].name === currentChapter) {
                        currentIndex = i;
                        break;
                    }
                }
                console.log("nextChapter - 当前索引:", currentIndex);

                // 如果不是最后一章，加载下一章
                if (currentIndex !== -1 && currentIndex < chapters.length - 1) {
                    const nextChapter = chapters[currentIndex + 1];
                    console.log("nextChapter - 下一章:", nextChapter);

                    // 检查是否是目录链接
                    if (nextChapter.isToc) {
                        // 如果是目录，加载子目录
                        loadSubDir(nextChapter.path.replace(/^\/?(books\/)?/, ''));
                    } else {
                        // 如果是普通章节，直接加载
                        await loadChapter(nextChapter.name, subDir);
                    }
                } else {
                    console.log('已经是最后一章');
                }
            }
            catch (error) {
                console.error('加载下一章失败:', error);
            }
        }

        async function prevChapter(subDir) {
            try {
                const chapters = await getChapterList(subDir);
                console.log("prevChapter - 当前章节:", currentChapter);
                console.log("prevChapter - 目录列表:", chapters);

                // 查找当前章节在列表中的索引
                let currentIndex = -1;
                for (let i = 0; i < chapters.length; i++) {
                    if (chapters[i].name === currentChapter) {
                        currentIndex = i;
                        break;
                    }
                }

                // 如果不是第一章，加载上一章
                if (currentIndex > 0) {
                    const prevChapter = chapters[currentIndex - 1];
                    // 检查是否是目录链接
                    if (prevChapter.isToc) {
                        // 如果是目录，加载子目录
                        loadSubDir(prevChapter.path.replace(/^\/?(books\/)?/, ''));
                    } else {
                        // 如果是普通章节，直接加载
                        await loadChapter(prevChapter.name, subDir);
                    }
                } else {
                    console.log('已经是第一章');
                }
            }
            catch (error) {
                console.error('加载上一章失败:', error);
            }
        }

        // 获取书籍列表
        async function fetchBooksList() {
            try {
                console.log('开始获取书籍列表...');
                const response = await fetch(`${BOOKS_DIR}bob.md`);
                if (!response.ok) {
                    throw new Error(`无法加载书籍列表: ${response.status}`);
                }

                const markdown = await response.text();
                console.log('books/bob.md内容:', markdown);

                const lines = markdown.split('\n').filter(line => line.trim());

                const books = [];
                for (const line of lines) {
                    const match = line.match(/- \[(.*?)\]\((.*?)\)/);
                    if (match) {
                        const name = match[1];
                        let path = match[2];

                        // 处理路径
                        // 如果路径以./开头，移除./
                        if (path.startsWith('./')) {
                            path = path.substring(2);
                        }

                        // 如果路径以/结尾，移除结尾的/
                        if (path.endsWith('/')) {
                            path = path.slice(0, -1);
                        }

                        // 提取目录部分（去掉文件名）
                        const pathParts = path.split('/');
                        if (pathParts.length > 0 && pathParts[pathParts.length - 1].includes('.md')) {
                            // 如果最后一部分是文件名，则移除
                            pathParts.pop();
                        }

                        const dirPath = pathParts.join('/');
                        console.log(`解析书籍路径: 名称=${name}, 原始路径=${path}, 处理后路径=${dirPath}`);

                        books.push({ name, path: dirPath });
                    }
                }

                console.log('解析到的书籍列表:', books);
                return books;
            } catch (error) {
                console.error('获取书籍列表失败:', error);
                return [];
            }
        }

        // 获取文件名列表
        async function getChapterList(subDir) {
            try {
                // 构建toc.md的路径
                const normalizedSubDir = subDir ? (subDir.endsWith('/') ? subDir : subDir + '/') : '';
                const tocUrl = `${BOOKS_DIR}${normalizedSubDir}toc.md`;
                console.log('正在读取目录文件:', tocUrl);

                // 读取toc.md内容
                const response = await fetch(tocUrl);
                if (!response.ok) {
                    console.error(`无法加载目录文件 ${tocUrl}: ${response.status}`);
                    throw new Error(`无法加载目录文件: ${response.status}`);
                }

                const res = await response.text();
                console.log(`${tocUrl} 文件内容:`, res);

                // 替换链接中的 "- " 前缀
                const markdown = res.replace(/- /g, '');

                // 使用marked解析markdown
                const tokens = marked.lexer(markdown);
                console.log('解析的markdown tokens:', tokens);

                // 提取所有链接
                const chapters = [];
                tokens.forEach(token => {
                    if (token.type === 'paragraph') {
                        token.tokens.forEach(token => {
                            if (token.type === 'link') {
                                // 从链接中提取完整路径信息
                                const href = token.href;
                                const text = token.text;
                                console.log('处理链接:', href, '文本:', text);

                                // 检查是否是目录链接（以toc.md结尾）
                                const isToc = href.endsWith('toc.md');
                                // 提取文件名或子目录名
                                let path, name;

                                if (isToc) {
                                    // 如果是toc.md链接，提取子目录名
                                    console.log('处理目录链接:', href);

                                    // 尝试从href中提取目录名
                                    let dirName = '';

                                    if (href.startsWith('./')) {
                                        // 相对路径
                                        const cleanPath = href.replace('./', '');
                                        const parts = cleanPath.split('/');
                                        if (parts.length > 0) {
                                            dirName = parts[0];
                                        }

                                        // 处理相对路径
                                        if (normalizedSubDir) {
                                            // 如果已经在子目录中，将当前目录添加到路径前面
                                            path = normalizedSubDir;
                                        } else {
                                            path = '';
                                        }

                                        // 添加目录名
                                        if (dirName) {
                                            path += dirName;
                                        }
                                    } else {
                                        // 绝对路径
                                        const match = href.match(/\/([^\/]+)\/toc\.md$/);
                                        if (match) {
                                            dirName = match[1];
                                            // 使用完整路径，但去掉books/前缀和最后的toc.md
                                            path = href.substring(0, href.lastIndexOf('/') + 1).replace(/^books\//, '');
                                        } else {
                                            // 处理简单的toc.md链接
                                            path = normalizedSubDir;
                                        }
                                    }

                                    name = dirName || text; // 如果无法从路径中提取，则使用链接文本
                                    console.log('目录名称:', name, '路径:', path);
                                } else {
                                    // 如果是普通章节，提取文件名
                                    console.log('处理章节链接:', href);

                                    if (href.startsWith('./')) {
                                        // 相对路径
                                        name = href.replace('./', '').replace('.md', '');
                                        path = normalizedSubDir;
                                    } else if (href.indexOf('/') === -1) {
                                        // 简单文件名，无路径
                                        name = href.replace('.md', '');
                                        path = normalizedSubDir;
                                    } else {
                                        // 绝对路径
                                        const match = href.match(/\/([^\/]+)\.md$/);
                                        if (match) {
                                            name = match[1];
                                            path = href.substring(0, href.lastIndexOf('/') + 1).replace(/^books\//, '');
                                        } else {
                                            // 简单文件名
                                            name = href.replace('.md', '');
                                            path = normalizedSubDir;
                                        }
                                    }
                                    console.log('章节名称:', name, '路径:', path);
                                }

                                if (name) {
                                    chapters.push({
                                        name: name,
                                        path: path,
                                        href: href,
                                        isToc: isToc
                                    });
                                    console.log('添加章节:', name, path, isToc);
                                } else {
                                    console.log('无法解析章节名称，跳过');
                                }
                            }
                        });
                    }
                });
                console.log('解析后的目录结构:', chapters);
                return chapters;
            } catch (error) {
                console.error('获取文件名列表失败:', error);
                return [];
            }
        }

        // 窗口大小变化时重新优化面包屑
        window.addEventListener('resize', function () {
            optimizeMobileBreadcrumb();
        });

        document.addEventListener("DOMContentLoaded", init);

        // 初始化 dialog 事件监听器
        document.addEventListener("DOMContentLoaded", function () {
            const dialog = document.getElementById('footnote-dialog');
            if (dialog) {
                // 监听 dialog 关闭事件
                dialog.addEventListener('close', function () {
                    // 确保在任何关闭方式下都恢复背景滚动
                    document.body.classList.remove('dialog-open');

                    // 检查搜索结果是否还在显示，如果是，保持搜索结果的模态状态
                    const floatingResultsContainer = document.getElementById('floating-results-container');
                    const floatingSearchContainer = document.getElementById('floating-search-container');

                    if (floatingResultsContainer && floatingResultsContainer.classList.contains('active')) {
                        // 搜索结果还在显示，保持模态状态
                        document.documentElement.classList.add('modal-open');
                        console.log('🔄 Dialog关闭事件：保持搜索结果弹窗状态');
                    } else if (floatingSearchContainer && floatingSearchContainer.classList.contains('active')) {
                        // 搜索框还在显示，保持模态状态
                        document.documentElement.classList.add('modal-open');
                        console.log('🔄 Dialog关闭事件：保持搜索框弹窗状态');
                    }
                });

                // 监听点击 backdrop 关闭 - 修复版本
                dialog.addEventListener('click', function (event) {
                    // 获取 dialog 的边界矩形
                    const rect = dialog.getBoundingClientRect();
                    const isInDialog = (
                        event.clientX >= rect.left &&
                        event.clientX <= rect.right &&
                        event.clientY >= rect.top &&
                        event.clientY <= rect.bottom
                    );

                    // 如果点击在 dialog 外部（backdrop 区域），关闭弹窗
                    if (!isInDialog) {
                        closeFootnoteDialog();
                    }
                });

                // 监听 ESC 键关闭
                document.addEventListener('keydown', function (event) {
                    if (event.key === 'Escape' && dialog.open) {
                        closeFootnoteDialog();
                    }
                });
            }
        });

        // 处理内容点击事件
        function handleContentClick(event) {
            const target = event.target;
            if (target.tagName === "A") {
                if (target.getAttribute("href") && target.getAttribute("href").startsWith('#')) {
                    return; // 让锚点链接正常工作
                }
                event.preventDefault(); // 只阻止.md文件链接的默认行为

                const href = target.getAttribute("href");
                if (href && href.includes(".md")) {
                    // 从href中提取路径信息
                    const pathParts = href.split('/');
                    const fileName = pathParts[pathParts.length - 1];

                    // 获取当前页面的 subDir
                    const params = new URLSearchParams(window.location.search);
                    const currentSubDir = params.get('dir') || '';

                    let relativePath = '';

                    // 处理相对路径（以 ./ 开头）
                    if (href.startsWith('./')) {
                        // 移除开头的 ./
                        const cleanPath = href.replace('./', '');
                        const cleanPathParts = cleanPath.split('/');

                        if (fileName === 'toc.md') {
                            // 如果是目录链接，保留父目录路径
                            if (currentSubDir) {
                                relativePath = currentSubDir + '/' + cleanPathParts[0];
                            } else {
                                relativePath = cleanPathParts[0];
                            }
                        } else {
                            // 如果是章节链接，需要构建完整的目录路径
                            if (cleanPathParts.length > 1) {
                                // 如果路径中包含子目录（如 ./赏析/3.md）
                                const subDirParts = cleanPathParts.slice(0, -1); // 移除文件名，保留目录部分
                                if (currentSubDir) {
                                    relativePath = currentSubDir + '/' + subDirParts.join('/');
                                } else {
                                    relativePath = subDirParts.join('/');
                                }
                            } else {
                                // 如果只是文件名（如 ./3.md），使用当前目录
                                relativePath = currentSubDir;
                            }
                        }
                    } else {
                        // 处理绝对路径
                        if (pathParts.length > 1) {
                            relativePath = pathParts.slice(0, -1)
                                .join('/')
                                .replace(/^books\//, '')  // 移除开头的 books/
                                .replace(/^\/+|\/+$/g, ''); // 移除开头和结尾的斜杠
                        } else {
                            relativePath = currentSubDir;
                        }
                    }

                    // 确保路径格式正确
                    relativePath = relativePath.replace(/^\/+|\/+$/g, '');

                    // 判断是否是目录链接
                    if (fileName === "toc.md") {
                        console.log('加载子目录:', relativePath);
                        loadSubDir(relativePath);
                    } else {
                        const chapterName = fileName.replace(".md", "");
                        console.log('即将加载章节:', chapterName, '路径:', relativePath);
                        loadChapter(chapterName, relativePath);
                    }
                }
            }
        }

        // 搜索函数
        async function performSearch() {
            console.log('开始执行搜索...');

            // 清除之前保存的原始搜索结果和排序状态
            window.originalSearchResults = null;
            window.sortState = 'original';
            window.allExpanded = false; // 重置展开/折叠状态

            const searchInput = document.getElementById('search-input');
            const searchTerm = searchInput.value.trim();
            const searchResults = document.getElementById('search-results');
            const floatingSearchContainer = document.getElementById('floating-search-container');
            const floatingResultsContainer = document.getElementById('floating-results-container');

            if (!searchTerm) {
                floatingResultsContainer.classList.remove('active');
                if (!floatingSearchContainer.classList.contains('active')) {
                    document.documentElement.classList.remove('modal-open');
                }
                return;
            }

            console.log(`搜索词: "${searchTerm}"`);

            // 使用原有搜索逻辑
            console.log('使用原有搜索逻辑');

            // 隐藏搜索框
            floatingSearchContainer.classList.remove('active');

            searchResults.innerHTML = '<div class="loading">搜索中...</div>';
            searchResults.classList.add('loading-state'); // 添加加载状态样式
            floatingResultsContainer.classList.add('active');
            document.documentElement.classList.add('modal-open');

            // 重置搜索结果列表的滚动位置到顶部
            searchResults.scrollTop = 0;

            try {
                console.log('搜索词:', searchTerm);
                // 如果搜索索引为空，先尝试加载或构建索引
                if (searchIndex.length === 0) {
                    console.log('搜索索引为空，尝试加载现有索引...');

                    // 显示加载进度
                    showLoadingProgress('正在加载搜索索引...');

                    const loaded = await loadSearchIndex();
                    if (!loaded) {
                        updateLoadingProgress('未找到现有索引，开始构建新索引...');
                        await buildSearchIndex();
                        console.log('搜索索引构建完成，索引大小:', searchIndex.length);
                        hideLoadingProgress();
                        // 提示用户可以下载索引文件
                        showIndexDownloadPrompt();
                    } else {
                        console.log('成功加载现有搜索索引，索引大小:', searchIndex.length);
                        hideLoadingProgress();
                    }
                }

                // 执行搜索
                console.log('开始搜索...');
                const results = originalSearchInIndex(searchTerm);
                console.log('搜索结果数量:', results.length);

                // 显示搜索结果
                displaySearchResults(results, searchTerm);
            } catch (error) {
                console.error('搜索失败:', error);
                searchResults.classList.remove('loading-state'); // 移除加载状态
                searchResults.innerHTML = `<div class="search-error">搜索出错: ${error.message}</div>`;
            }
        }

        // 加载现有搜索索引
        async function loadSearchIndex() {
            try {
                console.log('尝试加载搜索索引文件...');

                // 先检查元数据文件
                const metaResponse = await fetch('./search-meta.json');
                if (!metaResponse.ok) {
                    console.log('搜索索引元数据文件不存在');
                    return false;
                }

                const meta = await metaResponse.json();
                console.log('搜索索引元数据:', meta);

                // 检查索引是否有效（这里简单检查时间，后续可以加强）
                if (!isIndexValid(meta)) {
                    console.log('搜索索引已过期，需要重新构建');
                    return false;
                }

                // 检查是否为分块格式
                let indexData;
                if (meta.chunked) {
                    console.log(`检测到分块格式，加载 ${meta.chunkCount} 个分块文件...`);
                    indexData = await loadChunkedIndex(meta.chunkCount, meta);
                    if (!indexData) {
                        console.log('分块索引文件加载失败');
                        return false;
                    }
                } else {
                    // 加载单个索引数据文件
                    const indexResponse = await fetch('./search-index.json');
                    if (!indexResponse.ok) {
                        console.log('搜索索引数据文件不存在');
                        return false;
                    }
                    indexData = await indexResponse.json();
                }

                // 验证数据完整性
                if (!Array.isArray(indexData) || indexData.length === 0) {
                    console.log('搜索索引数据格式无效');
                    return false;
                }

                // 检查是否为优化格式，如果是则需要还原
                if (meta.optimized && meta.version === '1.1') {
                    console.log('检测到优化格式索引，正在还原...');
                    searchIndex = restoreOptimizedIndex(indexData);
                    console.log('优化格式索引还原完成');
                } else {
                    searchIndex = indexData;
                }

                indexLoaded = true;
                console.log(`成功加载搜索索引，包含 ${searchIndex.length} 个章节`);
                return true;

            } catch (error) {
                console.log('加载搜索索引失败:', error.message);
                return false;
            }
        }



        // 加载分块索引文件（支持智能分块）
        async function loadChunkedIndex(chunkCount, meta = {}) {
            try {
                const chunks = [];
                const loadPromises = [];
                const startTime = Date.now();

                // 显示分块类型信息
                const chunkMethod = meta.chunkMethod || 'legacy';
                console.log(`开始加载${chunkMethod === 'smart' ? '智能' : '传统'}分块索引，共 ${chunkCount} 个分块`);

                // 如果有分块详细信息，显示预期加载大小
                if (meta.chunkInfo && Array.isArray(meta.chunkInfo)) {
                    const totalSize = meta.chunkInfo.reduce((sum, info) => sum + parseFloat(info.sizeMB), 0);
                    console.log(`预期加载总大小: ${totalSize.toFixed(2)}MB`);
                }

                // 并行加载所有分块文件
                for (let i = 1; i <= chunkCount; i++) {
                    const promise = fetch(`./search-index-chunk-${i}.json`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`分块文件 ${i} 不存在`);
                            }
                            return response.json();
                        })
                        .then(chunkData => {
                            chunks[i - 1] = chunkData; // 保持顺序

                            // 显示详细的加载信息
                            const chunkInfo = meta.chunkInfo && meta.chunkInfo[i - 1];
                            if (chunkInfo) {
                                console.log(`分块 ${i}/${chunkCount} 加载完成 - ${chunkInfo.itemCount}项, ${chunkInfo.sizeMB}MB`);
                            } else {
                                console.log(`分块 ${i}/${chunkCount} 加载完成 - ${chunkData.length}项`);
                            }
                        });

                    loadPromises.push(promise);
                }

                // 等待所有分块加载完成
                await Promise.all(loadPromises);

                // 合并所有分块
                const mergedData = chunks.flat();
                const loadTime = ((Date.now() - startTime) / 1000).toFixed(2);

                console.log(`所有分块加载完成，合并后包含 ${mergedData.length} 个章节，耗时 ${loadTime}秒`);

                // 验证数据完整性
                if (meta.totalChapters && mergedData.length !== meta.totalChapters) {
                    console.warn(`数据完整性警告: 预期 ${meta.totalChapters} 个章节，实际加载 ${mergedData.length} 个`);
                }

                return mergedData;

            } catch (error) {
                console.error('加载分块索引失败:', error.message);
                return null;
            }
        }

        // 还原优化的索引数据
        function restoreOptimizedIndex(optimizedData) {
            return optimizedData.map(item => {
                const restored = {
                    title: item.t,           // t -> title
                    content: item.c || '',   // c -> content
                    bookName: item.bn,       // bn -> bookName
                    bookPath: item.bp,       // bp -> bookPath
                    chapterName: item.cn,    // cn -> chapterName
                    chapterPath: item.cp     // cp -> chapterPath
                };

                // 还原脚注信息（如果存在）
                if (item.f && Object.keys(item.f).length > 0) {
                    restored.footnotes = item.f;  // f -> footnotes
                }

                return restored;
            });
        }

        // 检查索引是否有效
        function isIndexValid(meta) {
            if (!meta.timestamp) return false;

            // 索引永不过期 - 已禁用过期检查
            // const indexAge = Date.now() - meta.timestamp;
            // const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
            // if (indexAge > maxAge) {
            //     console.log(`索引已过期，年龄: ${Math.round(indexAge / (24 * 60 * 60 * 1000))} 天`);
            //     return false;
            // }

            return true;
        }

        // 专门用于索引的脚注处理函数
        function processFootnotesForIndex(markdownText) {
            const footnotes = {};
            let cleanContent = markdownText;

            // 按空行分割成块
            const blocks = markdownText.split(/\n\s*\n/);
            const processedBlocks = [];

            let i = 0;
            while (i < blocks.length) {
                const block = blocks[i].trim();

                if (block === '') {
                    processedBlocks.push('');
                    i++;
                    continue;
                }

                // 检查是否包含脚注引用
                const footnoteRefs = block.match(/\[\^([^\]]+)\]/g) || [];

                if (footnoteRefs.length > 0) {
                    // 这是包含脚注的段落，收集紧跟的脚注定义
                    let processedParagraph = block;
                    const paragraphFootnotes = {};

                    // 提取段落开头的数字和可能的上中下后缀（节号）
                    const sectionMatch = processedParagraph.match(/^(\d+[上中下]?)\s+/);
                    const sectionNumber = sectionMatch ? sectionMatch[1] : null;

                    let j = i + 1;

                    // 收集紧跟的脚注定义
                    while (j < blocks.length) {
                        const nextBlock = blocks[j].trim();

                        if (nextBlock === '') {
                            j++;
                            continue;
                        }

                        // 检查是否为脚注定义块
                        const footnoteLines = nextBlock.split('\n');
                        let isFootnoteBlock = true;

                        for (const line of footnoteLines) {
                            if (line.trim() === '') continue;
                            if (!line.match(/^\[\^([^\]]+)\]:/)) {
                                isFootnoteBlock = false;
                                break;
                            }
                        }

                        if (isFootnoteBlock) {
                            // 解析这个脚注定义块
                            for (const line of footnoteLines) {
                                if (line.trim() === '') continue;
                                const footnoteDefMatch = line.match(/^\[\^([^\]]+)\]:\s*(.*)$/);
                                if (footnoteDefMatch) {
                                    const key = footnoteDefMatch[1];
                                    const content = footnoteDefMatch[2];
                                    paragraphFootnotes[key] = content;

                                    // 为重复的脚注键创建唯一标识
                                    let uniqueKey = key;
                                    let counter = 1;
                                    while (footnotes[uniqueKey]) {
                                        uniqueKey = `${key}_${counter}`;
                                        counter++;
                                    }

                                    // 添加到全局脚注集合，包含节号信息
                                    footnotes[uniqueKey] = {
                                        content: content,
                                        sectionNumber: sectionNumber,
                                        originalKey: key  // 保存原始键名
                                    };
                                }
                            }
                            j++;
                        } else {
                            break;
                        }
                    }

                    // 从段落中去除脚注标记
                    const footnoteKeys = [...new Set(footnoteRefs.map(ref => ref.match(/\[\^([^\]]+)\]/)[1]))];
                    footnoteKeys.forEach(key => {
                        const regex = new RegExp(`\\[\\^${key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\]`, 'g');
                        processedParagraph = processedParagraph.replace(regex, '');
                    });

                    processedBlocks.push(processedParagraph);
                    i = j; // 跳过已处理的脚注定义块
                } else {
                    // 普通段落，直接添加
                    processedBlocks.push(block);
                    i++;
                }
            }

            return {
                cleanContent: processedBlocks.join('\n\n'),
                footnotes: footnotes
            };
        }

        // 构建搜索索引
        async function buildSearchIndex() {
            searchIndex = [];

            try {
                // 获取书籍列表
                console.log('开始获取书籍列表...');
                const booksList = await fetchBooksList();
                console.log(`获取到 ${booksList.length} 本书`);

                // 遍历每本书，递归索引所有内容
                for (const book of booksList) {
                    await indexBookContent(book.name, book.path, 0);
                }

                console.log(`搜索索引构建完成，共索引 ${searchIndex.length} 个章节`);
            } catch (error) {
                console.error('构建搜索索引失败:', error);
                throw error;
            }
        }

        // 显示索引下载提示
        function showIndexDownloadPrompt() {
            // 创建提示框
            const promptDiv = document.createElement('div');
            promptDiv.id = 'index-download-prompt';
            promptDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ddeadc;
        border: 2px solid #006400;
        border-radius: 8px;
        padding: 15px;
        max-width: 300px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-size: 0.9rem;
      `;

            promptDiv.innerHTML = `
        <div style="margin-bottom: 10px; font-weight: bold; color: #006400;">
          🚀 搜索索引已构建完成！
        </div>
        <div style="margin-bottom: 15px; color: #333;">
          为了提升下次搜索速度，建议下载索引文件到网站根目录。
        </div>
        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
          <button onclick="downloadSearchIndex()" style="
            background: #006400;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
          ">下载索引</button>
          <button onclick="closeIndexPrompt()" style="
            background: #ccc;
            color: #333;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
          ">稍后</button>
        </div>
      `;

            document.body.appendChild(promptDiv);

            // 10秒后自动隐藏
            setTimeout(() => {
                closeIndexPrompt();
            }, 10000);
        }

        // 关闭索引下载提示
        function closeIndexPrompt() {
            const promptDiv = document.getElementById('index-download-prompt');
            if (promptDiv) {
                promptDiv.remove();
            }
        }

        // 优化索引数据以减少文件大小
        function optimizeIndexData(indexData) {
            return indexData.map(item => {
                // 压缩字段名
                const optimized = {
                    t: item.title,                    // title -> t
                    bn: item.bookName,               // bookName -> bn
                    bp: item.bookPath,               // bookPath -> bp
                    cn: item.chapterName,            // chapterName -> cn
                    cp: item.chapterPath             // chapterPath -> cp
                };

                // 内容优化：只保留前2000字符，并移除多余空白
                if (item.content) {
                    const content = item.content
                        .replace(/\s+/g, ' ')          // 多个空白字符合并为一个空格
                        .replace(/\n\s*\n/g, '\n\n')     // 多个换行合并为一个
                        .replace(/^#+\s+/g, '')        // 移除开头的'#'标记
                        .replace(/<.*?>/g, '')         // 移除HTML标签
                        .trim();                       // 移除首尾空白

                    optimized.c = content.substring(0, 2000); // content -> c，限制2000字符
                }

                // 添加脚注信息（如果存在）
                if (item.footnotes && Object.keys(item.footnotes).length > 0) {
                    optimized.f = item.footnotes;     // footnotes -> f
                }

                return optimized;
            });
        }

        // 下载搜索索引文件（带配置）
        function downloadSearchIndexWithConfig() {
            if (searchIndex.length === 0) {
                alert('搜索索引为空，请先进行搜索以构建索引');
                return;
            }

            // 获取用户配置的分块大小
            const chunkSizeSelect = document.getElementById('chunkSizeSelect');
            const targetChunkSizeMB = parseInt(chunkSizeSelect ? chunkSizeSelect.value : '5');

            downloadSearchIndex(targetChunkSizeMB);
        }

        // 下载搜索索引文件
        function downloadSearchIndex(customChunkSizeMB = 5) {
            if (searchIndex.length === 0) {
                alert('搜索索引为空，请先进行搜索以构建索引');
                return;
            }

            console.log('开始生成优化的搜索索引文件...');

            // 优化索引数据
            const optimizedIndex = optimizeIndexData(searchIndex);

            // 计算压缩率
            const originalSize = JSON.stringify(searchIndex).length;
            const optimizedSize = JSON.stringify(optimizedIndex).length;
            const compressionRatio = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);

            console.log(`索引优化完成：原始大小 ${(originalSize / 1024).toFixed(1)}KB，优化后 ${(optimizedSize / 1024).toFixed(1)}KB，压缩率 ${compressionRatio}%`);

            // 生成优化的索引数据（不使用格式化，减少文件大小）
            const indexData = JSON.stringify(optimizedIndex);

            // 生成元数据
            const metaData = JSON.stringify({
                version: '1.1',                    // 更新版本号表示优化格式
                timestamp: Date.now(),
                totalChapters: searchIndex.length,
                generatedAt: new Date().toISOString(),
                description: '优化的搜索索引文件',
                optimized: true,
                originalSize: originalSize,
                optimizedSize: optimizedSize,
                compressionRatio: compressionRatio + '%',
                chunkSizeMB: customChunkSizeMB    // 记录用户配置的分块大小
            });

            // 检查是否需要分块
            const shouldChunk = optimizedSize > 200 * 1024 * 1024; // 200MB以上分块

            if (shouldChunk) {
                console.log(`索引文件较大，使用智能分块（目标大小：${customChunkSizeMB}MB）...`);
                downloadChunkedIndex(optimizedIndex, metaData, customChunkSizeMB);
            } else {
                // 下载单个索引文件
                downloadFile('search-index.json', indexData);

                // 稍微延迟下载元数据文件，避免同时下载
                setTimeout(() => {
                    downloadFile('search-meta.json', metaData);
                }, 500);
            }

            // 关闭提示框
            closeIndexPrompt();

            // 显示使用说明
            setTimeout(() => {
                const chunkInfo = shouldChunk ? `\n• 分块大小：${customChunkSizeMB}MB` : '';
                alert(`优化索引文件下载完成！\n\n优化效果：\n• 文件大小减少 ${compressionRatio}%\n• 原始大小：${(originalSize / 1024).toFixed(1)}KB\n• 优化后：${(optimizedSize / 1024).toFixed(1)}KB${chunkInfo}\n\n使用说明：\n1. 将下载的文件放到网站根目录\n2. 下次搜索时将自动加载，大大提升速度\n3. 索引文件将持续有效，无需重新下载`);
            }, 1000);
        }

        // 下载文件的通用函数
        function downloadFile(filename, content) {
            const blob = new Blob([content], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log(`文件 ${filename} 下载完成`);
        }

        // 智能分块下载索引（按文件大小分块）
        function downloadChunkedIndex(optimizedIndex, metaData, customChunkSizeMB = 5) {
            const targetChunkSize = customChunkSizeMB * 1024 * 1024; // 目标分块大小
            const chunks = createSmartChunks(optimizedIndex, targetChunkSize);

            console.log(`索引智能分为 ${chunks.length} 个块，目标大小 ${customChunkSizeMB}MB`);

            // 更新元数据，添加分块信息
            const meta = JSON.parse(metaData);
            meta.chunked = true;
            meta.chunkCount = chunks.length;
            meta.chunkMethod = 'smart'; // 标记为智能分块
            meta.targetChunkSize = targetChunkSize;

            // 记录每个分块的详细信息
            meta.chunkInfo = chunks.map((chunk, index) => {
                const chunkData = JSON.stringify(chunk);
                return {
                    index: index + 1,
                    itemCount: chunk.length,
                    sizeBytes: new Blob([chunkData]).size,
                    sizeMB: (new Blob([chunkData]).size / 1024 / 1024).toFixed(2)
                };
            });

            // 下载元数据文件
            downloadFile('search-meta.json', JSON.stringify(meta, null, 2));

            // 下载各个分块文件
            chunks.forEach((chunk, index) => {
                setTimeout(() => {
                    const chunkData = JSON.stringify(chunk);
                    downloadFile(`search-index-chunk-${index + 1}.json`, chunkData);
                    console.log(`分块 ${index + 1}/${chunks.length} 下载完成，大小: ${meta.chunkInfo[index].sizeMB}MB`);
                }, (index + 1) * 300); // 每300ms下载一个文件
            });

            // 显示分块下载说明
            setTimeout(() => {
                const chunkSizeInfo = meta.chunkInfo.map(info =>
                    `• search-index-chunk-${info.index}.json (${info.itemCount}项, ${info.sizeMB}MB)`
                ).join('\n');

                alert(`智能分块索引文件下载完成！\n\n文件列表：\n• search-meta.json（元数据）\n${chunkSizeInfo}\n\n请将所有文件放到网站根目录`);
            }, chunks.length * 300 + 1000);
        }

        // 创建智能分块（按文件大小）
        function createSmartChunks(items, targetChunkSize) {
            const chunks = [];
            let currentChunk = [];
            let currentChunkSize = 0;

            // 预估JSON序列化的开销
            const jsonOverhead = 50; // 每个对象的JSON开销估算

            for (let i = 0; i < items.length; i++) {
                const item = items[i];

                // 估算当前项的序列化大小
                const itemSize = estimateItemSize(item) + jsonOverhead;

                // 检查是否需要开始新的分块
                if (currentChunk.length > 0 && (currentChunkSize + itemSize) > targetChunkSize) {
                    // 当前分块已满，开始新分块
                    chunks.push(currentChunk);
                    currentChunk = [item];
                    currentChunkSize = itemSize;
                } else {
                    // 添加到当前分块
                    currentChunk.push(item);
                    currentChunkSize += itemSize;
                }
            }

            // 添加最后一个分块
            if (currentChunk.length > 0) {
                chunks.push(currentChunk);
            }

            return chunks;
        }

        // 估算单个索引项的序列化大小
        function estimateItemSize(item) {
            let size = 0;

            // 计算各字段的大小
            size += (item.t || '').length * 2; // title，UTF-8字符按2字节计算
            size += (item.c || '').length * 2; // content
            size += (item.bn || '').length * 2; // bookName
            size += (item.bp || '').length * 2; // bookPath
            size += (item.cn || '').length * 2; // chapterName
            size += (item.cp || '').length * 2; // chapterPath

            // 计算脚注大小
            if (item.f && typeof item.f === 'object') {
                const footnoteStr = JSON.stringify(item.f);
                size += footnoteStr.length * 2;
            }

            // 添加JSON结构开销（括号、引号、逗号等）
            size += 200; // 估算的JSON结构开销

            return size;
        }

        // 显示加载进度
        function showLoadingProgress(message) {
            // 移除已存在的进度提示
            hideLoadingProgress();

            const progressDiv = document.createElement('div');
            progressDiv.id = 'loading-progress';
            progressDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px 30px;
        border-radius: 8px;
        z-index: 10002;
        text-align: center;
        font-size: 0.9rem;
        min-width: 200px;
      `;

            progressDiv.innerHTML = `
        <div style="margin-bottom: 10px;">${message}</div>
        <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px; overflow: hidden;">
          <div style="width: 100%; height: 100%; background: #006400; animation: pulse 1.5s ease-in-out infinite;"></div>
        </div>
        <style>
          @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
          }
        </style>
      `;

            document.body.appendChild(progressDiv);
        }

        // 更新加载进度消息
        function updateLoadingProgress(message) {
            const progressDiv = document.getElementById('loading-progress');
            if (progressDiv) {
                const messageDiv = progressDiv.querySelector('div');
                if (messageDiv) {
                    messageDiv.textContent = message;
                }
            }
        }

        // 隐藏加载进度
        function hideLoadingProgress() {
            const progressDiv = document.getElementById('loading-progress');
            if (progressDiv) {
                progressDiv.remove();
            }
        }

        // 显示索引管理界面
        function showIndexManagement() {
            // 创建管理界面
            const managementDiv = document.createElement('div');
            managementDiv.id = 'index-management';
            managementDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border: 2px solid #006400;
        border-radius: 12px;
        padding: 20px;
        max-width: 400px;
        width: 90%;
        z-index: 10001;
        box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        font-size: 0.9rem;
      `;

            const indexStatus = searchIndex.length > 0 ?
                `✅ 已加载 ${searchIndex.length} 个章节` :
                '❌ 索引未构建';

            const loadedFrom = indexLoaded ? '📁 从文件加载' : '🔨 实时构建';

            managementDiv.innerHTML = `
        <div style="margin-bottom: 15px; font-weight: bold; color: #006400; text-align: center;">
          🔧 搜索索引管理
        </div>

        <div style="margin-bottom: 15px; padding: 10px; background: #f5f5f5; border-radius: 6px;">
          <div style="margin-bottom: 5px;"><strong>当前状态：</strong> ${indexStatus}</div>
          <div style="margin-bottom: 5px;"><strong>加载方式：</strong> ${loadedFrom}</div>
          <div><strong>索引大小：</strong> ${(JSON.stringify(searchIndex).length / 1024).toFixed(1)} KB</div>
        </div>

        <div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border-radius: 6px; border: 1px solid #ddd;">
          <div style="margin-bottom: 8px; font-weight: bold; color: #333;">⚙️ 分块配置</div>
          <div style="margin-bottom: 8px; font-size: 0.8rem; color: #666;">
            大文件将自动分块下载，可配置分块大小：
          </div>
          <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
            <label style="font-size: 0.8rem; color: #333;">分块大小：</label>
            <select id="chunkSizeSelect" style="padding: 4px 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 0.8rem;">
              <option value="2">2MB (推荐)</option>
              <option value="5" selected>5MB (默认)</option>
              <option value="10">10MB</option>
              <option value="20">20MB</option>
            </select>
          </div>
          <div style="font-size: 0.75rem; color: #888;">
            • 较小分块：加载更快，文件更多<br>
            • 较大分块：文件更少，单次加载时间长
          </div>
        </div>

        <div style="margin-bottom: 15px; font-size: 0.8rem; color: #666; line-height: 1.4;">
          <strong>说明：</strong><br>
          • 下载索引文件可以大大提升搜索速度<br>
          • 将下载的文件放到网站根目录即可<br>
          • 索引文件将持续有效，无需重新下载<br>
          • 超过200MB的索引将自动使用智能分块
        </div>

        <div style="display: flex; gap: 8px; flex-wrap: wrap; justify-content: center;">
          <button onclick="downloadSearchIndexWithConfig()" style="
            background: #006400;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
          ">📥 下载索引</button>

          <button onclick="rebuildSearchIndex()" style="
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
          ">🔄 重建索引</button>

          <button onclick="closeIndexManagement()" style="
            background: #ccc;
            color: #333;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
          ">❌ 关闭</button>
        </div>
      `;

            // 添加背景遮罩
            const overlay = document.createElement('div');
            overlay.id = 'index-management-overlay';
            overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 10000;
      `;

            overlay.addEventListener('click', closeIndexManagement);

            // 禁止背景滚动
            document.documentElement.classList.add('modal-open');
            document.body.style.overflow = 'hidden';

            document.body.appendChild(overlay);
            document.body.appendChild(managementDiv);
        }

        // 关闭索引管理界面
        function closeIndexManagement() {
            const managementDiv = document.getElementById('index-management');
            const overlay = document.getElementById('index-management-overlay');

            // 恢复背景滚动
            document.documentElement.classList.remove('modal-open');
            document.body.style.overflow = '';

            if (managementDiv) managementDiv.remove();
            if (overlay) overlay.remove();
        }

        // 重建搜索索引
        async function rebuildSearchIndex() {
            if (!confirm('确定要重建搜索索引吗？这可能需要几秒钟时间。')) {
                return;
            }

            try {
                // 清空现有索引
                searchIndex = [];
                indexLoaded = false;

                console.log('开始重建搜索索引...');

                // 显示进度
                const managementDiv = document.getElementById('index-management');
                if (managementDiv) {
                    managementDiv.innerHTML = `
            <div style="text-align: center; padding: 20px;">
              <div style="margin-bottom: 15px; font-weight: bold; color: #006400;">
                🔄 正在重建索引...
              </div>
              <div style="margin-bottom: 10px;">请稍候，正在扫描所有章节</div>
              <div style="width: 100%; height: 4px; background: #f0f0f0; border-radius: 2px; overflow: hidden;">
                <div style="width: 100%; height: 100%; background: #006400; animation: pulse 1.5s ease-in-out infinite;"></div>
              </div>
            </div>
            <style>
              @keyframes pulse {
                0%, 100% { opacity: 0.6; }
                50% { opacity: 1; }
              }
            </style>
          `;
                }

                // 重建索引
                await buildSearchIndex();

                console.log('搜索索引重建完成');

                // 关闭管理界面
                closeIndexManagement();

                // 显示成功提示
                alert(`搜索索引重建完成！\n\n共索引 ${searchIndex.length} 个章节\n建议下载索引文件以提升下次搜索速度`);

                // 显示下载提示
                showIndexDownloadPrompt();

            } catch (error) {
                console.error('重建搜索索引失败:', error);
                alert('重建搜索索引失败: ' + error.message);
                closeIndexManagement();
            }
        }

        // 显示脚注弹窗 - 使用 dialog 元素
        function showFootnoteDialog(event, element) {
            event.preventDefault();
            event.stopPropagation();

            const content = element.getAttribute('data-footnote-content');
            const footnoteId = element.getAttribute('data-footnote-key') || element.textContent;
            const paragraphIndex = element.getAttribute('data-paragraph-index');
            const sectionNumber = element.getAttribute('data-section-number');

            // 获取当前页面的书籍和章节信息
            const params = new URLSearchParams(window.location.search);
            const currentDir = params.get('dir') || '';
            let bookName = '';
            let chapterName = currentChapter || '';

            // 从目录路径中提取书籍名
            if (currentDir) {
                const dirParts = currentDir.split('/').filter(part => part.trim() !== '');
                if (dirParts.length > 0) {
                    bookName = dirParts[dirParts.length - 1]; // 取最后一个部分作为书籍名
                }
            }

            // 解析脚注内容中的Markdown（支持分段）
            let processedContent = content
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>'); // 链接

            // 处理分段：将连续的换行转换为段落
            processedContent = processedContent
                .split('\n\n')  // 按双换行分割段落
                .map(paragraph => {
                    const trimmed = paragraph.trim();
                    if (trimmed === '') return '';
                    // 将单个段落包装在<p>标签中，保留段落内的单换行为<br>
                    return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`;
                })
                .filter(p => p !== '')  // 过滤空段落
                .join('');  // 连接所有段落

            // 使用新的标题生成函数
            const titleText = generateFootnoteTitle(bookName, chapterName, sectionNumber, footnoteId);

            // 获取 dialog 元素
            const dialog = document.getElementById('footnote-dialog');
            const titleElement = document.getElementById('footnote-title');
            const contentElement = document.getElementById('footnote-content');

            // 设置内容
            titleElement.textContent = titleText;
            contentElement.innerHTML = processedContent;

            // 禁用背景滚动
            document.body.classList.add('dialog-open');

            // 检查是否有搜索结果在显示，如果有，保持搜索结果的模态状态
            const floatingResultsContainer = document.getElementById('floating-results-container');
            const floatingSearchContainer = document.getElementById('floating-search-container');

            if ((floatingResultsContainer && floatingResultsContainer.classList.contains('active')) ||
                (floatingSearchContainer && floatingSearchContainer.classList.contains('active'))) {
                // 搜索相关弹窗还在显示，保持模态状态
                document.documentElement.classList.add('modal-open');
            }

            // 显示弹窗
            dialog.showModal();

            // 注意：使用 dialog.showModal() 不会影响页面滚动位置
            return false;
        }

        // 处理点击外部关闭脚注弹窗
        function handleOutsideClick(event) {
            const dialog = document.getElementById('footnote-dialog');
            if (!dialog || !dialog.open) return;

            const rect = dialog.getBoundingClientRect();
            const isInDialog = (
                event.clientX >= rect.left &&
                event.clientX <= rect.right &&
                event.clientY >= rect.top &&
                event.clientY <= rect.bottom
            );

            // 如果点击在 dialog 外部（backdrop 区域），关闭弹窗
            if (!isInDialog) {
                closeFootnoteDialog();
                // 移除事件监听器，避免重复添加
                document.removeEventListener('click', handleOutsideClick);
            }
        }

        // 添加触摸滚动调试功能
        function addTouchScrollDebug(contentElement) {
            // 使用简单的CSS方案，依赖浏览器原生触摸滚动
            console.log('脚注弹窗已创建，使用原生触摸滚动');
        }

        // 关闭脚注弹窗
        function closeFootnoteDialog() {
            const dialog = document.getElementById('footnote-dialog');
            dialog.close();

            // 移除点击外部关闭的事件监听器
            document.removeEventListener('click', handleOutsideClick);

            // 恢复背景滚动
            document.body.classList.remove('dialog-open');

            // 检查搜索结果是否还在显示，如果是，保持搜索结果的模态状态
            const floatingResultsContainer = document.getElementById('floating-results-container');
            const floatingSearchContainer = document.getElementById('floating-search-container');

            if (floatingResultsContainer && floatingResultsContainer.classList.contains('active')) {
                // 搜索结果还在显示，保持模态状态
                document.documentElement.classList.add('modal-open');
                console.log('🔄 脚注弹窗已关闭，保持搜索结果弹窗状态');
            } else if (floatingSearchContainer && floatingSearchContainer.classList.contains('active')) {
                // 搜索框还在显示，保持模态状态
                document.documentElement.classList.add('modal-open');
                console.log('🔄 脚注弹窗已关闭，保持搜索框弹窗状态');
            }
        }

        // 兼容旧的函数名 - 统一使用 dialog 版本
        function hideFootnotePopup() {
            closeFootnoteDialog();
        }

        // 直接显示脚注弹窗（用于搜索结果）- 使用 dialog 元素
        function showDirectFootnotePopup(footnoteKey, footnoteContent, searchTerm = '', bookName = '', chapterName = '', sectionNumber = '') {
            console.log(`🆕 Dialog版本脚注弹窗: 键="${footnoteKey}", 搜索词="${searchTerm}", 位置="${bookName} ${chapterName}:${sectionNumber}"`);

            // 高亮搜索词的函数
            function highlightSearchTerm(text, term) {
                if (!term) return text;
                const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                return text.replace(regex, '<mark style="background-color: yellow; font-weight: bold;">$1</mark>');
            }

            // 设置弹窗内容
            const highlightedContent = highlightSearchTerm(footnoteContent, searchTerm);
            const footnoteTitle = generateFootnoteTitle(bookName, chapterName, sectionNumber, footnoteKey);

            // 处理内容中的Markdown（与原有脚注弹窗保持一致）
            let processedContent = highlightedContent
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 粗体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')              // 斜体
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>') // 链接
                .split('\n\n')  // 按双换行分割段落
                .map(paragraph => {
                    const trimmed = paragraph.trim();
                    if (trimmed === '') return '';
                    // 将单个段落包装在<p>标签中，保留段落内的单换行为<br>
                    return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`;
                })
                .filter(p => p !== '')  // 过滤空段落
                .join('');  // 连接所有段落

            // 获取 dialog 元素
            const dialog = document.getElementById('footnote-dialog');
            const titleElement = document.getElementById('footnote-title');
            const contentElement = document.getElementById('footnote-content');

            // 设置内容
            titleElement.textContent = footnoteTitle;
            contentElement.innerHTML = processedContent;

            // 禁用背景滚动（保持搜索结果的模态状态）
            document.body.classList.add('dialog-open');
            // 确保搜索结果的模态状态也保持
            document.documentElement.classList.add('modal-open');

            // 显示弹窗
            dialog.showModal();

            // 注意：使用 dialog.showModal() 不会影响页面滚动位置

            // 添加触摸滚动调试
            addTouchScrollDebug(contentElement);

            // 延迟添加点击外部关闭的事件监听器
            setTimeout(() => {
                document.addEventListener('click', handleOutsideClick);
            }, 100);

            console.log('✅ 新版本脚注弹窗已显示 - 纯CSS样式');
        }

        // 显示经文弹窗（用于搜索结果）- 使用 dialog 元素
        function showVersePopup(verseData, searchTerm = '') {
            console.log(`📖 显示经文弹窗: ${verseData.title}, 搜索词="${searchTerm}"`);

            // 关闭已存在的弹窗
            hideFootnotePopup();

            // 高亮搜索词的函数
            function highlightSearchTerm(text, term) {
                if (!term) return text;
                const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                return text.replace(regex, '<mark style="background-color: yellow; font-weight: bold;">$1</mark>');
            }

            // 处理经文内容
            const highlightedContent = highlightSearchTerm(verseData.content, searchTerm);

            // 构建正确的标题
            let displayTitle = verseData.title;
            if (verseData.actualBookName && verseData.chapterName && verseData.verseNumber) {
                displayTitle = `${verseData.actualBookName}${verseData.chapterName}:${verseData.verseNumber}`;
            }

            // 处理内容格式（与脚注弹窗保持一致）
            const processedContent = highlightedContent
                .split(/\n\s*\n/)  // 按双换行分割段落
                .map(paragraph => {
                    const trimmed = paragraph.trim();
                    if (trimmed === '') return '';
                    // 将单个段落包装在<p>标签中，保留段落内的单换行为<br>
                    return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`;
                })
                .filter(p => p !== '')  // 过滤空段落
                .join('');  // 连接所有段落

            // 获取 dialog 元素
            const dialog = document.getElementById('footnote-dialog');
            const titleElement = document.getElementById('footnote-title');
            const contentElement = document.getElementById('footnote-content');

            // 设置内容
            titleElement.textContent = `📖 ${displayTitle}`;
            contentElement.innerHTML = processedContent;

            // 禁用背景滚动（保持搜索结果的模态状态）
            document.body.classList.add('dialog-open');
            // 确保搜索结果的模态状态也保持
            document.documentElement.classList.add('modal-open');

            // 显示弹窗
            dialog.showModal();

            // 延迟添加点击外部关闭的事件监听器
            setTimeout(() => {
                document.addEventListener('click', handleOutsideClick);
            }, 100);

            console.log('✅ 经文弹窗已显示 - 使用 dialog 元素');

            // 延迟添加点击外部关闭的事件监听器
            setTimeout(() => {
                document.addEventListener('click', handleOutsideClick);
            }, 100);

            console.log('✅ 经文弹窗已显示 - 使用 dialog 元素');
        }

        // 版本检查函数
        function checkFootnoteVersion() {
            console.log('=== 脚注弹窗版本检查 ===');
            console.log('当前时间:', new Date().toLocaleString());
            console.log('showDirectFootnotePopup函数存在:', typeof showDirectFootnotePopup === 'function');
            console.log('版本: 2025-07-12 新版本 - 纯CSS样式');

            // 测试弹窗
            showDirectFootnotePopup('test', '这是版本检查测试弹窗，应该使用纯CSS样式', '', '测试', '版本', '检查');
        }

        // 全屏章节弹窗功能
        function loadChapterInModal(bookPath, chapterName, searchTerm = '', customTitle = '', clickedElement = null, matchPosition = 1) {
            console.log(`📖 在弹窗中加载章节: ${bookPath}/${chapterName}, 搜索词="${searchTerm}", 匹配位置=${matchPosition}`);

            // 创建全屏弹窗
            const dialog = document.createElement('dialog');
            dialog.id = 'chapter-modal-dialog';
            dialog.className = 'chapter-modal-dialog';

            // 生成弹窗标题
            const modalTitle = customTitle || generateModalTitle(bookPath, chapterName, clickedElement);

            dialog.innerHTML = `
                <div class="chapter-modal-content">
                    <div class="chapter-modal-header">
                        <h2 class="chapter-modal-title">${modalTitle}</h2>
                        <div class="chapter-modal-controls">
                            <button id="close-chapter-modal-btn" class="modal-close-btn" title="关闭">✕</button>
                        </div>
                    </div>
                    <div class="chapter-modal-body">
                        <div id="chapter-modal-loading" class="modal-loading">
                            <div class="loading-spinner"></div>
                            <p>正在加载章节内容...</p>
                        </div>
                        <div id="chapter-modal-content" class="modal-chapter-content" style="display: none;"></div>
                    </div>
                </div>
            `;

            // 添加到页面
            document.body.appendChild(dialog);

            // 显示弹窗
            dialog.showModal();

            // 绑定关闭事件
            const closeBtn = dialog.querySelector('#close-chapter-modal-btn');
            closeBtn.addEventListener('click', () => {
                closeChapterModal();
            });

            // 绑定ESC键关闭
            dialog.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeChapterModal();
                }
            });

            // 绑定点击背景关闭
            dialog.addEventListener('click', (e) => {
                if (e.target === dialog) {
                    closeChapterModal();
                }
            });

            // 异步加载章节内容
            loadChapterContentInModal(bookPath, chapterName, searchTerm, matchPosition);

            // 保存当前状态用于导航
            window.currentModalChapter = {
                bookPath: bookPath,
                chapterName: chapterName,
                searchTerm: searchTerm,
                matchPosition: matchPosition
            };
        }

        // 关闭全屏章节弹窗
        function closeChapterModal() {
            const dialog = document.getElementById('chapter-modal-dialog');
            if (dialog) {
                dialog.close();
                dialog.remove();
            }
            window.currentModalChapter = null;
        }

        // 处理全屏弹窗中的链接点击 - 在当前弹窗中加载新内容
        function handleModalLinkClick(event, href, currentBookPath) {
            event.preventDefault();

            console.log('弹窗链接点击:', href, '当前路径:', currentBookPath);

            if (href && href.includes(".md")) {
                // 从href中提取路径信息
                const pathParts = href.split('/');
                const fileName = pathParts[pathParts.length - 1];

                let relativePath = '';

                // 处理相对路径（以 ./ 开头）
                if (href.startsWith('./')) {
                    // 移除开头的 ./
                    const cleanPath = href.replace('./', '');
                    const cleanPathParts = cleanPath.split('/');

                    if (fileName === 'toc.md') {
                        // 如果是目录链接，保留父目录路径
                        if (currentBookPath) {
                            relativePath = currentBookPath + cleanPathParts[0];
                        } else {
                            relativePath = cleanPathParts[0];
                        }
                    } else {
                        // 如果是章节链接，需要构建完整的目录路径
                        if (cleanPathParts.length > 1) {
                            // 如果路径中包含子目录（如 ./赏析/3.md）
                            const subDirParts = cleanPathParts.slice(0, -1); // 移除文件名，保留目录部分
                            if (currentBookPath) {
                                relativePath = currentBookPath + subDirParts.join('/');
                            } else {
                                relativePath = subDirParts.join('/');
                            }
                        } else {
                            // 如果只是文件名（如 ./3.md），使用当前目录
                            relativePath = currentBookPath;
                        }
                    }
                } else {
                    // 处理绝对路径
                    if (pathParts.length > 1) {
                        relativePath = pathParts.slice(0, -1)
                            .join('/')
                            .replace(/^books\//, '')  // 移除开头的 books/
                            .replace(/^\/+|\/+$/g, ''); // 移除开头和结尾的斜杠
                    } else {
                        relativePath = currentBookPath;
                    }
                }

                // 确保路径格式正确
                relativePath = relativePath.replace(/^\/+|\/+$/g, '');

                // 不关闭弹窗，而是在当前弹窗中加载新内容
                const chapterName = fileName.replace(".md", "");
                console.log('在弹窗中加载新章节:', chapterName, '路径:', relativePath);

                // 更新弹窗标题
                const titleElement = document.querySelector('.chapter-modal-title');
                if (titleElement) {
                    titleElement.textContent = `正在加载 ${chapterName}...`;
                }

                // 在当前弹窗中加载新内容
                loadChapterContentInModal(relativePath, chapterName, '', 1);

                // 更新当前弹窗状态
                if (window.currentModalChapter) {
                    window.currentModalChapter.bookPath = relativePath;
                    window.currentModalChapter.chapterName = chapterName;
                }
            }
        }

        // 在弹窗中加载章节内容
        async function loadChapterContentInModal(bookPath, chapterName, searchTerm = '', matchPosition = 1) {
            const loadingDiv = document.getElementById('chapter-modal-loading');
            const contentDiv = document.getElementById('chapter-modal-content');

            try {
                // 显示加载状态
                loadingDiv.style.display = 'block';
                contentDiv.style.display = 'none';

                // 构建文件路径 - 修复双斜杠问题
                let normalizedBookPath = bookPath;
                if (!normalizedBookPath.endsWith('/')) {
                    normalizedBookPath += '/';
                }

                // 构建完整路径，添加books前缀
                const filePath = `books/${normalizedBookPath}${chapterName}.md`;
                console.log(`加载章节文件: ${filePath}`);

                // 加载文件内容
                const response = await fetch(filePath);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const content = await response.text();

                // 处理内容（转换Markdown等）
                const processedContent = await processChapterContent(content, bookPath, chapterName);

                // 显示内容
                contentDiv.innerHTML = processedContent;

                // 更新弹窗标题 - 只在没有从搜索结果提取标题时才更新
                const titleElement = document.querySelector('.chapter-modal-title');
                if (titleElement) {
                    // 检查是否已经有从搜索结果提取的标题（包含 " > " 或 " - "）
                    const currentTitle = titleElement.textContent;
                    const hasExtractedTitle = currentTitle.includes(' > ') ||
                        (currentTitle.includes(' - ') && currentTitle.split(' - ').length > 2);

                    // 只有在没有提取标题的情况下才更新
                    if (!hasExtractedTitle) {
                        // 尝试从处理后的内容中提取第一个h1标题
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = processedContent;
                        const h1Element = tempDiv.querySelector('h1');

                        if (h1Element) {
                            titleElement.textContent = h1Element.textContent;
                        } else {
                            // 如果没有h1标题，使用章节名称
                            titleElement.textContent = `${bookPath.replace(/\/$/, '').split('/').pop()} - ${chapterName}`;
                        }
                    }
                }

                // 隐藏加载状态，显示内容
                loadingDiv.style.display = 'none';
                contentDiv.style.display = 'block';

                // 如果有搜索词，进行高亮
                if (searchTerm) {
                    highlightModalSearchTerm(searchTerm, matchPosition);
                }

                console.log('✅ 章节内容加载完成');

            } catch (error) {
                console.error('❌ 加载章节内容失败:', error);

                // 显示错误信息
                contentDiv.innerHTML = `
                    <div class="error-message">
                        <h3>加载失败</h3>
                        <p>无法加载章节内容: ${error.message}</p>
                        <button onclick="closeChapterModal()" class="retry-btn">关闭</button>
                    </div>
                `;

                loadingDiv.style.display = 'none';
                contentDiv.style.display = 'block';
            }
        }

        // 生成弹窗标题 - 从搜索结果标题中提取
        function generateModalTitle(bookPath, chapterName, clickedElement = null) {
            console.log(`生成弹窗标题: bookPath="${bookPath}", chapterName="${chapterName}"`);

            // 尝试从点击的元素中获取搜索结果标题
            if (clickedElement) {
                try {
                    // 向上查找搜索结果容器
                    let resultContainer = clickedElement.closest('.search-result-item');
                    if (resultContainer) {
                        // 查找标题元素
                        const titleElement = resultContainer.querySelector('.search-result-title');
                        if (titleElement) {
                            // 提取标题文本，去掉匹配计数和展开图标
                            let titleText = titleElement.textContent || titleElement.innerText;

                            // 清理标题文本
                            titleText = titleText
                                .replace(/\(\d+处匹配\)/g, '') // 移除匹配计数
                                .replace(/[▼▶]/g, '') // 移除展开图标
                                .replace(/\s+/g, ' ') // 规范化空白字符
                                .trim(); // 去掉首尾空白

                            if (titleText) {
                                console.log(`从搜索结果标题提取: "${titleText}"`);
                                return titleText;
                            }
                        }
                    }
                } catch (error) {
                    console.warn('从搜索结果标题提取失败:', error);
                }
            }

            // 回退到原来的逻辑
            if (bookPath.includes('圣经研读')) {
                // 圣经研读格式：创世记 1:1
                const pathParts = bookPath.split('/');
                const bookName = pathParts.length >= 2 ? pathParts[pathParts.length - 2] : '';
                return `${bookName} ${chapterName}`;
            } else {
                // 其他分类格式：完整路径 > 章节名
                const pathParts = bookPath.split('/').filter(part => part);
                const displayPath = pathParts.slice(0, -1).join(' > '); // 去掉最后的空字符串
                return `${displayPath} > ${chapterName}`;
            }
        }

        // 滚动到指定的匹配位置
        function scrollToTargetMatch(element, matchNumber, totalMatches) {
            if (!element) return;

            console.log(`🎯 滚动到第 ${matchNumber} 个匹配位置（共 ${totalMatches} 个）`);

            // 获取弹窗内容容器
            const modalContent = document.getElementById('chapter-modal-content');
            if (!modalContent) return;

            // 计算元素相对于容器的位置
            const elementRect = element.getBoundingClientRect();
            const containerRect = modalContent.getBoundingClientRect();

            // 计算滚动位置（将匹配元素滚动到容器中央偏上的位置）
            const scrollTop = modalContent.scrollTop + elementRect.top - containerRect.top - 100;

            // 平滑滚动到目标位置
            modalContent.scrollTo({
                top: Math.max(0, scrollTop),
                behavior: 'smooth'
            });

            // 添加临时的闪烁效果来突出显示目标匹配
            element.style.transition = 'background-color 0.3s ease';
            element.style.backgroundColor = '#ff6b6b';

            setTimeout(() => {
                element.style.backgroundColor = '#ffeb3b'; // 恢复原来的高亮颜色
            }, 600);

            console.log(`✅ 已滚动到第 ${matchNumber} 个匹配位置`);
        }

        // 处理章节内容（转换Markdown等）
        async function processChapterContent(content, bookPath, chapterName) {
            try {
                // 使用现有的Markdown处理逻辑
                if (typeof marked !== 'undefined') {
                    // 处理脚注 - 检查函数是否存在
                    let processedMarkdown = content;
                    let footnotes = {};

                    if (typeof processFootnotes === 'function') {
                        const result = processFootnotes(content);
                        processedMarkdown = result.markdownText;
                        footnotes = result.footnotes;
                    }

                    // 配置marked渲染器
                    const renderer = new marked.Renderer();

                    // 处理链接 - 参考页面主要内容的链接处理逻辑
                    renderer.link = function (token) {
                        const href = token.href;
                        const title = token.title;
                        const text = token.text;

                        if (href && href.includes('.md')) {
                            // 为.md文件创建可跳转的链接
                            return `<a href="#" onclick="handleModalLinkClick(event, '${href}', '${bookPath}')" title="${title || ''}">${text}</a>`;
                        }
                        return `<a href="${href}" title="${title || ''}" target="_blank">${text}</a>`;
                    };

                    // 使用与页面主要内容相同的图片扩展
                    const imageExtension = {
                        name: 'image',
                        renderer(token) {
                            let href = token.href;
                            const title = token.title;
                            const text = token.text;

                            // 修复图片路径处理逻辑
                            const relPrefixes = ['http', 'https', 'data:', '//', '/', '#'];
                            if (!relPrefixes.some(prefix => href.startsWith(prefix))) {
                                const basePath = bookPath ? `books/${bookPath}` : 'books';
                                if (href.startsWith('./')) {
                                    // 相对路径：./img/da1.png -> books/诗歌合辑/大本诗歌/img/da1.png
                                    href = `${basePath}${href.substring(1)}`;
                                } else {
                                    // 直接路径：img/da1.png -> books/诗歌合辑/大本诗歌/img/da1.png
                                    href = `${basePath}/${href}`;
                                }
                            }

                            // 生成图片HTML，添加错误处理
                            let html = `<img src="${href}" alt="${text || ''}"`;

                            if (title) {
                                html += ` title="${title}"`;
                            }

                            // 添加样式和错误处理
                            html += ` style="max-width: 100%; height: auto;" loading="lazy"`;
                            html += ` onerror="this.style.display='none'; console.error('图片加载失败:', this.src);"`;
                            html += `>`;

                            return html;
                        }
                    };

                    // 使用扩展和renderer
                    marked.use({
                        extensions: [imageExtension],
                        renderer: renderer,
                        breaks: true,
                        gfm: true
                    });

                    // 转换Markdown为HTML
                    const htmlContent = marked.parse(processedMarkdown);

                    // 重置marked配置（避免影响其他地方）
                    marked.use({ extensions: [] });

                    // 处理脚注引用
                    return htmlContent.replace(/\[\^([^\]]+)\]/g,
                        '<sup class="footnote-ref" data-footnote="$1" onclick="showFootnoteFromModal(\'$1\', arguments[0])">$1</sup>');
                } else {
                    // 如果marked不可用，使用简单处理
                    let processedContent = content
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/\n/g, '<br>');

                    if (processedContent && !processedContent.startsWith('<')) {
                        processedContent = `<p>${processedContent}</p>`;
                    }

                    return processedContent.replace(/\[\^([^\]]+)\]/g,
                        '<sup class="footnote-ref" data-footnote="$1">$1</sup>');
                }
            } catch (error) {
                console.error('处理章节内容失败:', error);
                // 返回简单处理的内容
                return content.replace(/\n/g, '<br>');
            }
        }

        // 在弹窗中高亮搜索词
        function highlightModalSearchTerm(searchTerm, targetMatchPosition = 1) {
            const contentDiv = document.getElementById('chapter-modal-content');
            if (!contentDiv || !searchTerm) return;

            console.log(`在弹窗中高亮搜索词: "${searchTerm}", 目标匹配位置: ${targetMatchPosition}`);

            // 创建正则表达式
            const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');

            // 遍历文本节点并高亮
            const walker = document.createTreeWalker(
                contentDiv,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: function (node) {
                        return node.parentNode.tagName !== 'SCRIPT' ?
                            NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                    }
                }
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                if (regex.test(node.textContent)) {
                    textNodes.push(node);
                }
            }

            // 高亮匹配的文本并收集所有高亮元素
            const allHighlights = [];
            textNodes.forEach((textNode) => {
                const highlightedText = textNode.textContent.replace(regex,
                    '<mark class="search-highlight">$1</mark>');

                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = highlightedText;

                const parent = textNode.parentNode;
                while (tempDiv.firstChild) {
                    parent.insertBefore(tempDiv.firstChild, textNode);
                }
                parent.removeChild(textNode);

                // 收集这个文本节点中的所有高亮元素
                const highlights = parent.querySelectorAll('mark.search-highlight');
                highlights.forEach(highlight => allHighlights.push(highlight));
            });

            console.log(`✅ 搜索词高亮完成，处理了 ${textNodes.length} 个文本节点，共找到 ${allHighlights.length} 个匹配`);

            // 滚动到指定位置的匹配
            if (allHighlights.length > 0) {
                // 确保目标位置在有效范围内
                const targetIndex = Math.min(Math.max(targetMatchPosition - 1, 0), allHighlights.length - 1);
                const targetHighlight = allHighlights[targetIndex];

                console.log(`🎯 滚动到第 ${targetIndex + 1} 个匹配位置（共 ${allHighlights.length} 个匹配）`);

                setTimeout(() => {
                    scrollToTargetMatch(targetHighlight, targetIndex + 1, allHighlights.length);
                }, 100); // 稍微延迟以确保DOM更新完成
            }
        }

        // 从弹窗中显示脚注
        function showFootnoteFromModal(footnoteKey, event) {
            console.log(`从弹窗中显示脚注: ${footnoteKey}`);

            // 这里可以实现弹窗中的脚注显示逻辑
            // 暂时使用简单的alert，后续可以改为弹窗内的脚注显示
            alert(`脚注 ${footnoteKey}: 这里将显示脚注内容`);

            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
        }

        // 解析圣经经文节的函数
        function parseVerses(chapterContent, bookName, chapterName, chapterPath) {
            const verses = [];
            const lines = chapterContent.split('\n');

            console.log(`📖 开始解析经文节: ${bookName}${chapterName}`);

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;

                // 匹配节号格式：1　、2上　、15下　等
                const verseMatch = trimmedLine.match(/^(\d+[上中下]?)\s+(.+)$/);
                if (verseMatch) {
                    const verseNumber = verseMatch[1];
                    let verseText = verseMatch[2];

                    // 去除脚注标记，只保留经文正文
                    const cleanText = verseText.replace(/\[\^[^\]]+\]/g, '');

                    if (cleanText.trim()) {
                        verses.push({
                            title: `${bookName}${chapterName}:${verseNumber}`,
                            content: cleanText.trim(),
                            verseNumber: verseNumber,
                            bookName: "圣经研读", // 设置为"圣经"以便正确分类
                            actualBookName: bookName, // 保存实际的书卷名
                            chapterName: chapterName,
                            bookPath: chapterPath, // 使用传入的正确路径
                            chapterPath: chapterPath,
                            type: "verse"
                        });

                        console.log(`  📝 解析节: ${verseNumber} - "${cleanText.substring(0, 30)}..."`);
                    }
                }
            }

            console.log(`📊 ${bookName}${chapterName} 共解析出 ${verses.length} 节经文`);
            return verses;
        }

        // 为经文节分配属于该节的脚注
        function getFootnotesForVerse(allFootnotes, verseNumber) {
            const verseFootnotes = {};

            for (const [key, data] of Object.entries(allFootnotes)) {
                let sectionNumber = '';

                // 获取脚注的节号
                if (typeof data === 'object' && data.sectionNumber) {
                    sectionNumber = data.sectionNumber;
                }

                // 只有当脚注属于该节时才添加
                if (sectionNumber === verseNumber) {
                    verseFootnotes[key] = data;
                }
            }

            return verseFootnotes;
        }

        // 生成脚注标题的通用函数
        function generateFootnoteTitle(bookName, chapterName, sectionNumber, footnoteKey) {
            let title = '';

            // 添加书籍和章节信息
            if (bookName && chapterName) {
                title += `${bookName}${chapterName}`;
                if (sectionNumber) {
                    title += `:${sectionNumber}`;
                }
            }

            // 根据脚注键类型添加前缀
            let footnoteDisplay = '';
            if (/^\d+$/.test(footnoteKey)) {
                // 数字脚注：显示为"注1", "注2"等
                footnoteDisplay = `注${footnoteKey}`;
            } else if (/^[a-zA-Z]$/.test(footnoteKey)) {
                // 字母脚注：显示为"串a", "串b"等
                footnoteDisplay = `串${footnoteKey}`;
            } else {
                // 其他格式：直接显示
                footnoteDisplay = `[^${footnoteKey}]`;
            }

            if (title) {
                return `${title} ${footnoteDisplay}`;
            } else {
                return `脚注 ${footnoteDisplay}`;
            }
        }

        // 检查搜索索引中的脚注数据
        function checkFootnoteIndex() {
            console.log('=== 搜索索引脚注检查 ===');
            console.log('搜索索引大小:', searchIndex.length);

            if (searchIndex.length === 0) {
                console.log('❌ 搜索索引为空，请先进行搜索以构建索引');
                return;
            }

            let totalFootnotes = 0;
            let itemsWithFootnotes = 0;
            let newFormatCount = 0;
            let oldFormatCount = 0;

            // 检查前5个索引项
            console.log('\n前5个索引项的脚注情况:');
            for (let i = 0; i < Math.min(5, searchIndex.length); i++) {
                const item = searchIndex[i];
                const bookName = item.bookName || item.bn || '';
                const title = item.title || item.t || '';
                const footnotes = item.footnotes || item.f || {};

                console.log(`\n${i + 1}. ${bookName} - ${title}`);
                console.log(`   脚注对象:`, footnotes);
                console.log(`   脚注数量:`, footnotes ? Object.keys(footnotes).length : 0);

                if (footnotes && Object.keys(footnotes).length > 0) {
                    itemsWithFootnotes++;
                    for (const [key, data] of Object.entries(footnotes)) {
                        totalFootnotes++;
                        if (typeof data === 'string') {
                            oldFormatCount++;
                            console.log(`   [^${key}] (旧格式): ${data.substring(0, 50)}...`);
                        } else if (typeof data === 'object' && data.content) {
                            newFormatCount++;
                            console.log(`   [^${key}] (新格式): 节号="${data.sectionNumber}", 内容="${data.content.substring(0, 50)}..."`);
                        } else {
                            console.log(`   [^${key}] (未知格式):`, data);
                        }
                    }
                }
            }

            console.log(`\n=== 统计结果 ===`);
            console.log(`包含脚注的索引项: ${itemsWithFootnotes}/${searchIndex.length}`);
            console.log(`总脚注数: ${totalFootnotes}`);
            console.log(`新格式脚注: ${newFormatCount}`);
            console.log(`旧格式脚注: ${oldFormatCount}`);

            // 测试脚注搜索
            console.log(`\n=== 测试脚注搜索 ===`);
            const testTerm = '王下';
            let matchCount = 0;

            for (const item of searchIndex) {
                if (item.footnotes) {
                    for (const [key, data] of Object.entries(item.footnotes)) {
                        let content = '';
                        if (typeof data === 'string') {
                            content = data;
                        } else if (typeof data === 'object' && data.content) {
                            content = data.content;
                        }

                        if (content.toLowerCase().includes(testTerm.toLowerCase())) {
                            matchCount++;
                            console.log(`匹配 ${matchCount}: [^${key}] 在 ${bookName}/${title} - ${content.substring(0, 30)}...`);
                        }
                    }
                }
            }

            console.log(`搜索"${testTerm}"找到 ${matchCount} 个脚注匹配`);
        }

        // 测试脚注提取功能
        function testFootnoteExtraction() {
            console.log('=== 测试脚注提取功能 ===');

            // 测试用的Markdown内容（来自实际文档）
            const testContent = `1　当犹大王[^a]乌西雅，以色列王约阿施的儿子[^b]耶罗波安在位的日子，大[^c]地震前二年，提哥亚牧羊人中的[^1]阿摩司[^2]得了论以色列的话。

[^1]:意，负重担者。阿摩司预言的中心思想，与何西阿和约珥预言的中心思想极为相似，就是耶和华惩罚列国并惩治以色列，好为着大卫的国(九11～12，徒十五15～16)，即基督的国(启十一15)，带进复兴时代(太十九28)。

[^2]:直译，看见。

[^a]:　王下十四21；代下二六1；赛一1；何一1

[^b]:　王下十四23；摩七9～11

[^c]:　亚十四5

2　他说，[^a]耶和华必从锡安吼叫，从耶路撒冷发声；牧人的草场要悲哀，迦密的山顶要枯干。

[^a]:　耶二五30；珥三16`;

            console.log('原始内容:');
            console.log(testContent);
            console.log('\n=== 使用 processFootnotesForIndex 处理 ===');

            try {
                const result = processFootnotesForIndex(testContent);
                console.log('提取的脚注数量:', Object.keys(result.footnotes).length);
                console.log('脚注详情:');
                for (const [key, data] of Object.entries(result.footnotes)) {
                    console.log(`[^${key}]:`, data);
                }
                console.log('\n清理后的内容:');
                console.log(result.cleanContent);

                // 检查是否还有脚注标记
                const remainingMarks = result.cleanContent.match(/\[\^[^\]]+\]/g);
                if (remainingMarks) {
                    console.log('❌ 警告：清理后的内容仍包含脚注标记:', remainingMarks);
                } else {
                    console.log('✅ 清理后的内容已去除所有脚注标记');
                }

                return result;
            } catch (error) {
                console.error('脚注处理出错:', error);
                return null;
            }
        }

        // 测试重复脚注键的处理
        function testDuplicateFootnotes() {
            console.log('=== 测试重复脚注键处理 ===');

            // 模拟有重复脚注键的内容
            const testContent = `1　第一段内容[^1]和[^a]脚注。

[^1]:第一个脚注1的内容。
[^a]:第一个脚注a的内容。

2　第二段内容[^1]和[^a]脚注。

[^1]:第二个脚注1的内容（不同于第一个）。
[^a]:第二个脚注a的内容（不同于第一个）。

3　第三段内容[^1]脚注。

[^1]:第三个脚注1的内容（又不同）。`;

            console.log('测试内容:');
            console.log(testContent);
            console.log('\n=== 处理结果 ===');

            const result = processFootnotesForIndex(testContent);
            console.log('\n=== 分析结果 ===');
            console.log('提取的脚注总数:', Object.keys(result.footnotes).length);
            console.log('预期应该有6个不同的脚注（3个[^1] + 3个[^a]）');

            // 按原始键分组
            const groupedByOriginal = {};
            for (const [uniqueKey, data] of Object.entries(result.footnotes)) {
                const originalKey = data.originalKey || uniqueKey;
                if (!groupedByOriginal[originalKey]) {
                    groupedByOriginal[originalKey] = [];
                }
                groupedByOriginal[originalKey].push({ uniqueKey, content: data.content });
            }

            console.log('\n按原始键分组:');
            for (const [originalKey, variants] of Object.entries(groupedByOriginal)) {
                console.log(`[^${originalKey}] 有 ${variants.length} 个变体:`);
                variants.forEach((variant, index) => {
                    console.log(`  ${index + 1}. [^${variant.uniqueKey}]: ${variant.content}`);
                });
            }

            return result;
        }

        // 检查经文节索引功能
        function checkVerseIndex() {
            console.log('=== 经文节索引检查 ===');
            console.log('搜索索引大小:', searchIndex.length);

            if (searchIndex.length === 0) {
                console.log('❌ 搜索索引为空，请先进行搜索以构建索引');
                return;
            }

            // 统计不同类型的索引项
            let verseCount = 0;
            let chapterCount = 0;
            let otherCount = 0;

            const verseExamples = [];
            const chapterExamples = [];

            for (const item of searchIndex) {
                const title = item.title || item.t || '';
                const content = item.content || item.c || '';
                const bookName = item.bookName || item.bn || '';
                const bookPath = item.bookPath || item.bp || '';

                if (item.type === 'verse') {
                    verseCount++;
                    if (verseExamples.length < 5) {
                        verseExamples.push(`${title}: "${content.substring(0, 30)}..."`);
                    }
                } else if (bookPath && bookPath.includes('圣经研读')) {
                    chapterCount++;
                    if (chapterExamples.length < 3) {
                        chapterExamples.push(`${bookName} - ${title}`);
                    }
                } else {
                    otherCount++;
                }
            }

            console.log(`\n=== 索引统计 ===`);
            console.log(`经文节索引: ${verseCount} 个`);
            console.log(`圣经章节索引: ${chapterCount} 个`);
            console.log(`其他索引: ${otherCount} 个`);

            console.log(`\n=== 经文节示例 ===`);
            verseExamples.forEach((example, index) => {
                console.log(`${index + 1}. ${example}`);
            });

            console.log(`\n=== 圣经章节示例 ===`);
            chapterExamples.forEach((example, index) => {
                console.log(`${index + 1}. ${example}`);
            });

            // 检查分类设置
            console.log(`\n=== 分类检查 ===`);
            const bibleItems = searchIndex.filter(item => {
                const bookName = item.bookName || item.bn || '';
                return bookName === '圣经研读';
            });
            console.log(`bookName为"圣经"的索引项: ${bibleItems.length} 个`);

            if (bibleItems.length > 0) {
                console.log('前3个圣经索引项:');
                bibleItems.slice(0, 3).forEach((item, index) => {
                    const title = item.title || item.t || '';
                    console.log(`${index + 1}. ${title} (类型: ${item.type || '章节'})`);
                });
            }

            // 检查是否正确应用了修改
            if (verseCount > 0) {
                console.log('✅ 经文节索引功能已正确应用');
                if (bibleItems.length === verseCount) {
                    console.log('✅ 分类设置正确，所有经文节都归类为"圣经"');
                } else {
                    console.log('⚠️ 分类可能有问题，请检查');
                }
            } else {
                console.log('❌ 经文节索引功能未生效，请检查代码');
            }

            // 检查脚注分配
            console.log(`\n=== 脚注分配检查 ===`);
            let versesWithFootnotes = 0;
            let versesWithoutFootnotes = 0;
            let totalFootnotesInVerses = 0;

            const verseItems = searchIndex.filter(item => item.type === 'verse');
            verseItems.forEach(verse => {
                const footnoteCount = verse.footnotes ? Object.keys(verse.footnotes).length : 0;
                if (footnoteCount > 0) {
                    versesWithFootnotes++;
                    totalFootnotesInVerses += footnoteCount;
                } else {
                    versesWithoutFootnotes++;
                }
            });

            console.log(`有脚注的经文节: ${versesWithFootnotes} 个`);
            console.log(`无脚注的经文节: ${versesWithoutFootnotes} 个`);
            console.log(`经文节中的脚注总数: ${totalFootnotesInVerses} 个`);

            // 显示前几个有脚注的经文节
            console.log(`\n前5个有脚注的经文节:`);
            verseItems.filter(v => v.footnotes && Object.keys(v.footnotes).length > 0)
                .slice(0, 5)
                .forEach((verse, index) => {
                    const footnoteKeys = Object.keys(verse.footnotes);
                    console.log(`${index + 1}. ${verse.title}: ${footnoteKeys.length}个脚注 [${footnoteKeys.join(', ')}]`);
                });

            return { verseCount, chapterCount, otherCount, bibleItems: bibleItems.length, versesWithFootnotes, versesWithoutFootnotes };
        }

        // 调试脚注匹配问题
        function debugFootnoteMatching(searchTerm = '王下') {
            console.log('=== 调试脚注匹配问题 ===');
            console.log('搜索词:', searchTerm);

            if (searchIndex.length === 0) {
                console.log('❌ 搜索索引为空，请先进行搜索');
                return;
            }

            // 查找包含搜索词的脚注
            let footnoteMatches = [];

            for (const item of searchIndex) {
                const footnotes = item.footnotes || item.f || {};
                if (footnotes && Object.keys(footnotes).length > 0) {
                    for (const [key, data] of Object.entries(footnotes)) {
                        let content = '';
                        let sectionNumber = '';
                        let originalKey = key;

                        if (typeof data === 'string') {
                            content = data;
                        } else if (typeof data === 'object' && data.content) {
                            content = data.content;
                            sectionNumber = data.sectionNumber || '';
                            originalKey = data.originalKey || key;
                        }

                        if (content.toLowerCase().includes(searchTerm.toLowerCase())) {
                            footnoteMatches.push({
                                item: item,
                                footnoteKey: key,
                                originalKey: originalKey,
                                content: content,
                                sectionNumber: sectionNumber,
                                bookPath: item.bookPath || item.bp || '',
                                chapterName: item.chapterName || item.cn || '',
                                itemType: item.type || 'chapter',
                                itemTitle: item.title || item.t || ''
                            });
                        }
                    }
                }
            }

            console.log(`找到 ${footnoteMatches.length} 个脚注匹配:`);
            footnoteMatches.slice(0, 10).forEach((match, index) => {
                console.log(`${index + 1}. 键="${match.originalKey}" (唯一键: ${match.footnoteKey})`);
                console.log(`   索引项: ${match.itemTitle} (类型: ${match.itemType})`);
                console.log(`   路径: ${match.bookPath}/${match.chapterName}`);
                console.log(`   节号: ${match.sectionNumber}`);
                console.log(`   内容: "${match.content.substring(0, 80)}..."`);
                console.log('');
            });

            // 检查重复的脚注键
            const keyGroups = {};
            footnoteMatches.forEach(match => {
                const key = match.originalKey;
                if (!keyGroups[key]) keyGroups[key] = [];
                keyGroups[key].push(match);
            });

            console.log('=== 重复脚注键分析 ===');
            for (const [key, matches] of Object.entries(keyGroups)) {
                if (matches.length > 1) {
                    console.log(`脚注键 "${key}" 有 ${matches.length} 个不同的内容:`);
                    matches.forEach((match, index) => {
                        console.log(`  ${index + 1}. 节号="${match.sectionNumber}" - "${match.content.substring(0, 40)}..."`);
                    });
                }
            }

            return footnoteMatches;
        }

        // 在控制台暴露检查函数
        window.checkFootnoteVersion = checkFootnoteVersion;
        window.checkFootnoteIndex = checkFootnoteIndex;
        window.testFootnoteExtraction = testFootnoteExtraction;
        window.testDuplicateFootnotes = testDuplicateFootnotes;
        window.checkVerseIndex = checkVerseIndex;
        window.debugFootnoteMatching = debugFootnoteMatching;

        // 检测并自动打开脚注弹窗（用于原始搜索结果）
        function detectAndOpenFootnotePopup(searchTerm) {
            console.log(`检测脚注内容匹配: 搜索词="${searchTerm}"`);

            // 查找所有脚注链接
            const footnoteLinks = document.querySelectorAll('.footnote-ref');
            console.log(`页面中共找到 ${footnoteLinks.length} 个脚注链接`);

            // 检查是否有脚注内容包含搜索词
            let matchingFootnotes = [];

            for (const link of footnoteLinks) {
                const footnoteContent = link.getAttribute('data-footnote-content') || '';
                if (footnoteContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                    matchingFootnotes.push({
                        link: link,
                        content: footnoteContent,
                        key: link.getAttribute('data-footnote-key'),
                        sectionNumber: link.getAttribute('data-section-number')
                    });
                }
            }

            console.log(`找到 ${matchingFootnotes.length} 个匹配的脚注`);

            if (matchingFootnotes.length > 0) {
                // 如果找到匹配的脚注，自动打开第一个
                const firstMatch = matchingFootnotes[0];
                console.log(`自动打开第一个匹配的脚注: 键="${firstMatch.key}", 节号="${firstMatch.sectionNumber}"`);

                // 高亮显示找到的脚注链接
                firstMatch.link.style.backgroundColor = 'yellow';
                firstMatch.link.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 延迟一点时间后触发脚注弹窗
                setTimeout(() => {
                    firstMatch.link.style.backgroundColor = '';
                    firstMatch.link.click();
                    console.log(`已自动打开脚注弹窗`);
                }, 800);

                return true; // 表示找到并打开了脚注
            } else {
                console.log(`未找到包含搜索词"${searchTerm}"的脚注内容`);
                // 如果没有找到脚注匹配，进行正常的内容高亮
                setTimeout(() => {
                    highlightContentSearchTerm(searchTerm, '', 0);
                }, 100);
                return false;
            }
        }

        // 自动打开脚注弹窗（用于搜索结果跳转）
        function autoOpenFootnotePopup(footnoteKey, searchTerm, sectionNumber = null) {
            console.log(`尝试自动打开脚注弹窗: 脚注键="${footnoteKey}", 搜索词="${searchTerm}", 节号="${sectionNumber}"`);

            // 查找所有脚注链接
            const footnoteLinks = document.querySelectorAll('.footnote-ref');
            console.log(`页面中共找到 ${footnoteLinks.length} 个脚注链接`);

            let targetFootnoteLink = null;
            let matchedLinks = [];

            // 如果有脚注键，优先通过脚注键匹配
            if (footnoteKey && footnoteKey.trim() !== '') {
                // 方法1: 直接通过脚注键匹配
                for (const link of footnoteLinks) {
                    const linkFootnoteKey = link.getAttribute('data-footnote-key');
                    if (linkFootnoteKey === footnoteKey) {
                        matchedLinks.push(link);
                    }
                }

                // 如果有多个匹配的脚注键，尝试通过节号进一步筛选
                if (matchedLinks.length > 1 && sectionNumber) {
                    console.log(`找到 ${matchedLinks.length} 个匹配的脚注键，尝试通过节号筛选: "${sectionNumber}"`);
                    for (const link of matchedLinks) {
                        const linkSectionNumber = link.getAttribute('data-section-number');
                        if (linkSectionNumber === sectionNumber) {
                            targetFootnoteLink = link;
                            console.log(`通过脚注键和节号匹配找到目标脚注: ${footnoteKey} (节号: ${sectionNumber})`);
                            break;
                        }
                    }
                } else if (matchedLinks.length === 1) {
                    targetFootnoteLink = matchedLinks[0];
                    console.log(`通过脚注键直接匹配找到目标脚注: ${footnoteKey}`);
                } else if (matchedLinks.length > 1) {
                    // 如果有多个匹配但没有节号信息，选择第一个
                    targetFootnoteLink = matchedLinks[0];
                    console.log(`找到多个匹配的脚注键，选择第一个: ${footnoteKey}`);
                }
            }

            // 方法2: 如果脚注键匹配失败或没有脚注键，通过脚注内容匹配
            if (!targetFootnoteLink) {
                console.log(`脚注键匹配失败，尝试通过内容匹配搜索词: "${searchTerm}"`);
                for (const link of footnoteLinks) {
                    const footnoteContent = link.getAttribute('data-footnote-content') || '';
                    if (footnoteContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                        targetFootnoteLink = link;
                        console.log(`通过内容匹配找到目标脚注: 键="${link.getAttribute('data-footnote-key')}", 内容="${footnoteContent.substring(0, 30)}..."`);
                        break;
                    }
                }
            }

            // 如果找到目标脚注链接，自动打开弹窗
            if (targetFootnoteLink) {
                console.log(`找到目标脚注链接，准备自动打开弹窗`);
                console.log(`目标脚注链接信息: 键="${targetFootnoteLink.getAttribute('data-footnote-key')}", 节号="${targetFootnoteLink.getAttribute('data-section-number')}"`);

                // 高亮显示找到的脚注链接
                targetFootnoteLink.style.backgroundColor = 'yellow';
                targetFootnoteLink.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // 延迟一点时间后触发脚注弹窗
                setTimeout(() => {
                    targetFootnoteLink.style.backgroundColor = '';
                    console.log(`正在触发脚注链接点击事件...`);
                    targetFootnoteLink.click();
                    console.log(`已触发脚注链接点击事件`);
                }, 800);
            } else {
                console.log(`❌ 未找到匹配的脚注链接，搜索词: "${searchTerm}", 脚注键: "${footnoteKey}", 节号: "${sectionNumber}"`);
                console.log('可用的脚注链接:');
                footnoteLinks.forEach((link, index) => {
                    const key = link.getAttribute('data-footnote-key');
                    const linkSectionNumber = link.getAttribute('data-section-number');
                    const content = link.getAttribute('data-footnote-content') || '';
                    console.log(`  ${index + 1}. 键="${key}", 节号="${linkSectionNumber}", 内容="${content.substring(0, 30)}..."`);
                });
            }
        }



        // 递归索引书籍内容
        async function indexBookContent(bookName, bookPath, depth) {
            console.log(`[深度${depth}] 索引内容: ${bookName}, 路径: ${bookPath}`);

            // 获取章节列表
            const chapters = await getChapterList(bookPath);
            console.log(`[深度${depth}] ${bookName} 有 ${chapters.length} 个条目`);

            // 遍历每个章节或子目录
            for (const chapter of chapters) {
                if (chapter.isToc) {
                    // 如果是目录，递归处理
                    console.log(`[深度${depth}] 发现子目录: ${chapter.name}, 路径: ${chapter.path}`);

                    // 提取子目录路径
                    let subDirPath = chapter.path;
                    if (chapter.path.endsWith('/')) {
                        subDirPath = chapter.path.slice(0, -1);
                    }

                    // 构建子目录的完整路径
                    let fullSubDirPath;
                    if (chapter.href.startsWith('./')) {
                        // 相对路径
                        if (bookPath) {
                            fullSubDirPath = `${bookPath}/${chapter.name}`;
                        } else {
                            fullSubDirPath = chapter.name;
                        }
                    } else {
                        // 绝对路径
                        fullSubDirPath = subDirPath.replace(/^\/+|\/+$/g, '');
                    }

                    console.log(`[深度${depth}] 递归处理子目录: ${fullSubDirPath}`);

                    // 递归处理子目录
                    await indexBookContent(`${bookName} > ${chapter.name}`, fullSubDirPath, depth + 1);
                } else {
                    // 如果是普通章节，添加到索引
                    try {
                        // 读取章节内容
                        const normalizedPath = chapter.path ? (chapter.path.endsWith('/') ? chapter.path : chapter.path + '/') : '';
                        const chapterPath = `${BOOKS_DIR}${normalizedPath}${chapter.name}.md`;
                        console.log(`[深度${depth}] 读取章节内容: ${chapterPath}`);

                        const response = await fetch(chapterPath);
                        if (!response.ok) {
                            console.error(`[深度${depth}] 无法加载章节 ${chapterPath}: ${response.status}`);
                            continue;
                        }

                        const content = await response.text();
                        console.log(`[深度${depth}] 章节 ${chapter.name} 内容长度: ${content.length}`);

                        // 提取标题
                        let title = chapter.name;
                        const titleMatch = content.match(/^#\s+(.+)$/m);
                        if (titleMatch) {
                            title = titleMatch[1];
                        }

                        // 处理脚注，提取脚注信息并去除脚注标记
                        let footnotes = {};
                        let cleanContent = content; // 默认使用原始内容

                        // 只对圣经目录进行脚注处理
                        const isBibleDirectory = bookPath.includes('圣经研读/') || bookPath.includes('圣经研读\\');

                        if (isBibleDirectory) {
                            try {
                                const result = processFootnotesForIndex(content);
                                footnotes = result.footnotes;
                                cleanContent = result.cleanContent; // 使用去除脚注标记的内容
                                console.log(`[深度${depth}] 📖 圣经目录，提取了 ${Object.keys(footnotes).length} 个脚注`);
                                console.log(`[深度${depth}] 原始内容长度: ${content.length}, 清理后长度: ${cleanContent.length}`);

                                // 解析经文节并添加到索引
                                const verses = parseVerses(cleanContent, bookName.split(' > ').pop(), chapter.name, chapter.path);
                                for (const verse of verses) {
                                    // 为每个经文节分配属于该节的脚注
                                    verse.footnotes = getFootnotesForVerse(footnotes, verse.verseNumber);
                                    searchIndex.push(verse);
                                }
                                console.log(`[深度${depth}] 📝 添加了 ${verses.length} 个经文节到索引`);

                                // 对于圣经目录，只添加经文节，不添加整章
                                console.log(`[深度${depth}] 📖 圣经目录使用节级别索引，跳过章级别索引`);

                            } catch (error) {
                                console.warn(`[深度${depth}] 脚注处理失败:`, error);
                                // 如果脚注处理失败，仍然添加整章到索引
                                searchIndex.push({
                                    title,
                                    content: cleanContent,
                                    footnotes,
                                    bookName,
                                    bookPath: chapter.path,
                                    chapterName: chapter.name,
                                    chapterPath: chapter.path
                                });
                                console.log(`[深度${depth}] 脚注处理失败，添加整章到索引: ${bookName} - ${title}`);
                            }
                        } else {
                            console.log(`[深度${depth}] 📚 非圣经目录，跳过脚注处理`);

                            // 非圣经目录，添加整章到索引
                            searchIndex.push({
                                title,
                                content: cleanContent,
                                footnotes,
                                bookName,
                                bookPath: chapter.path,
                                chapterName: chapter.name,
                                chapterPath: chapter.path
                            });
                            console.log(`[深度${depth}] 添加到索引: ${bookName} - ${title}`);
                        }
                    } catch (err) {
                        console.error(`[深度${depth}] 无法索引章节 ${chapter.name}:`, err);
                    }
                }
            }
        }



        // 在文本中查找所有匹配项
        function findMatchesInText(text, searchTerm) {
            const matches = [];
            const textLower = text.toLowerCase();
            const searchLower = searchTerm.toLowerCase();
            let index = textLower.indexOf(searchLower);

            while (index !== -1) {
                matches.push({ index: index });
                index = textLower.indexOf(searchLower, index + searchTerm.length);
            }

            return matches;
        }

        // 生成上下文
        function generateContext(text, matchIndex, matchLength) {
            let contextStart = Math.max(0, matchIndex - 50);
            let contextEnd = Math.min(text.length, matchIndex + matchLength + 50);

            // 确保上下文不会截断单词
            while (contextStart > 0 && !/\s/.test(text[contextStart])) {
                contextStart--;
            }
            while (contextEnd < text.length && !/\s/.test(text[contextEnd])) {
                contextEnd++;
            }

            let context = text.substring(contextStart, contextEnd);

            // 添加省略号
            if (contextStart > 0) {
                context = '...' + context;
            }
            if (contextEnd < text.length) {
                context += '...';
            }

            return context;
        }

        // 移动端面包屑优化
        function optimizeMobileBreadcrumb() {
            // 只在移动端执行
            if (window.innerWidth > 768) return;

            const breadcrumbNav = document.getElementById('breadcrumb-nav');
            if (!breadcrumbNav || !breadcrumbNav.classList.contains('show')) return;

            // 移动端不隐藏任何项目，只是调整样式以适应小屏幕
            // 可以选择换行显示或水平滚动
            breadcrumbNav.style.whiteSpace = 'nowrap';
            breadcrumbNav.style.overflowX = 'auto';
            breadcrumbNav.style.overflowY = 'hidden';
        }

        // 搜索方法
        function originalSearchInIndex(searchTerm) {
            const results = [];
            const searchTermLower = searchTerm.toLowerCase();
            console.log(`在 ${searchIndex.length} 条索引中搜索: "${searchTermLower}"`);

            // 记录已添加的章节，避免重复（但经文节不去重）
            const addedChapters = new Set();

            for (const item of searchIndex) {
                // 兼容优化后的字段名
                const content = item.content || item.c || '';
                const title = item.title || item.t || '';
                const footnotes = item.footnotes || item.f || {};

                const contentLower = content.toLowerCase();
                const titleLower = title.toLowerCase();

                // 兼容优化后的字段名
                const bookPath = item.bookPath || item.bp || '';
                const chapterName = item.chapterName || item.cn || '';
                const bookName = item.bookName || item.bn || '';

                // 对于经文类型，使用更精确的标识符
                let itemKey;
                if (item.type === 'verse') {
                    itemKey = `${bookPath}/${chapterName}:${item.verseNumber}`;
                } else {
                    itemKey = `${bookPath}/${chapterName}`;
                }

                // 检查是否已添加过这个项目
                if (addedChapters.has(itemKey)) {
                    continue;
                }

                // 收集所有匹配位置
                let allMatches = [];

                // 检查标题匹配
                if (titleLower.includes(searchTermLower)) {
                    allMatches.push({
                        type: 'title',
                        index: titleLower.indexOf(searchTermLower),
                        position: 0
                    });
                }

                // 检查内容中的所有匹配
                let contentMatchIndex = contentLower.indexOf(searchTermLower);
                let matchCount = 0;
                while (contentMatchIndex !== -1) {
                    const contentBeforeMatch = content.substring(0, contentMatchIndex);
                    const lineNumber = (contentBeforeMatch.match(/\n/g) || []).length + 1;

                    // 对于经文类型，使用特殊的匹配类型
                    const matchType = item.type === 'verse' ? 'verse' : 'content';

                    allMatches.push({
                        type: matchType,
                        index: contentMatchIndex,
                        position: matchCount + 1,
                        lineNumber: lineNumber,
                        // 为经文匹配添加额外信息
                        verseNumber: item.type === 'verse' ? item.verseNumber : undefined
                    });

                    matchCount++;
                    contentMatchIndex = contentLower.indexOf(searchTermLower, contentMatchIndex + searchTermLower.length);
                }

                // 检查脚注中的匹配
                if (footnotes && Object.keys(footnotes).length > 0) {
                    Object.entries(footnotes).forEach(([footnoteKey, footnoteData]) => {
                        // 处理新的脚注数据结构
                        let footnoteContent, sectionNumber, originalKey;
                        if (typeof footnoteData === 'string') {
                            // 兼容旧格式
                            footnoteContent = footnoteData;
                            sectionNumber = '';
                            originalKey = footnoteKey;
                        } else {
                            // 新格式
                            footnoteContent = footnoteData.content;
                            sectionNumber = footnoteData.sectionNumber || '';
                            originalKey = footnoteData.originalKey || footnoteKey;
                        }

                        const footnoteContentLower = footnoteContent.toLowerCase();
                        if (footnoteContentLower.includes(searchTermLower)) {
                            const footnoteMatchIndex = footnoteContentLower.indexOf(searchTermLower);
                            allMatches.push({
                                type: 'footnote',
                                index: footnoteMatchIndex,
                                position: allMatches.length + 1,
                                footnoteKey: originalKey,  // 使用原始键名显示
                                footnoteContent: footnoteContent,
                                sectionNumber: sectionNumber,
                                lineNumber: 0 // 脚注没有行号概念
                            });
                            console.log(`✅ 找到脚注匹配: 键="${originalKey}" (唯一键: ${footnoteKey}), 节号="${sectionNumber}", 内容="${footnoteContent.substring(0, 30)}..."`);
                        }
                    });
                }

                // 如果有匹配项，处理结果
                if (allMatches.length > 0) {
                    addedChapters.add(itemKey);

                    const contexts = allMatches.slice(0, 5).map(match => {
                        let text, context;

                        if (match.type === 'title') {
                            text = title;
                            context = generateContext(text, match.index, searchTerm.length);
                        } else if (match.type === 'footnote') {
                            text = match.footnoteContent;
                            context = generateContext(text, match.index, searchTerm.length);
                        } else {
                            text = content;
                            context = generateContext(text, match.index, searchTerm.length);
                        }

                        const contextObj = {
                            context,
                            type: match.type,
                            position: match.position,
                            lineNumber: match.lineNumber || 0
                        };

                        // 如果是脚注匹配，添加脚注相关信息
                        if (match.type === 'footnote') {
                            contextObj.footnoteKey = match.footnoteKey;
                            contextObj.footnoteContent = match.footnoteContent;
                            contextObj.sectionNumber = match.sectionNumber || '';
                        }
                        // 如果是经文匹配，添加经文相关信息
                        else if (match.type === 'verse') {
                            contextObj.verseNumber = match.verseNumber;
                        }

                        return contextObj;
                    });

                    results.push({
                        title: title,
                        contexts: contexts,
                        bookName: bookName,
                        bookPath: bookPath,
                        chapterName: chapterName,
                        chapterPath: item.chapterPath || item.cp || '',
                        matchCount: allMatches.length
                    });
                }
            }

            console.log(`搜索完成，找到 ${results.length} 个结果`);
            return results;
        }


        // 分类筛选功能
        function filterResultsByCategory(selectedCategory) {
            console.log('=== 开始分类筛选 ===');
            console.log('筛选分类:', selectedCategory);
            console.log('window.allSearchResults 是否存在:', !!window.allSearchResults);
            console.log('window.allSearchResults 长度:', window.allSearchResults ? window.allSearchResults.length : 0);

            if (!window.allSearchResults) {
                console.error('没有可筛选的搜索结果');
                return;
            }

            // 检查数据完整性
            console.log('前5个搜索结果的bookName:');
            window.allSearchResults.slice(0, 5).forEach((result, index) => {
                console.log(`  ${index + 1}. "${result.bookName}"`);
            });

            let filteredResults;
            if (selectedCategory === 'all') {
                filteredResults = window.allSearchResults;
            } else {
                // 筛选一级分类：提取bookName的第一部分
                console.log(`开始筛选分类"${selectedCategory}"，总数据量: ${window.allSearchResults.length}`);

                // 先统计所有分类
                const allCategories = {};
                window.allSearchResults.forEach(result => {
                    let firstLevel = '';
                    if (result.bookName.includes(' > ')) {
                        firstLevel = result.bookName.split(' > ')[0];
                    } else if (result.bookName.includes('/')) {
                        firstLevel = result.bookName.split('/')[0];
                    } else {
                        firstLevel = result.bookName;
                    }
                    firstLevel = firstLevel.trim();
                    allCategories[firstLevel] = (allCategories[firstLevel] || 0) + 1;
                });
                console.log('全部搜索结果中的分类统计:', allCategories);

                filteredResults = window.allSearchResults.filter((result, index) => {
                    // 尝试多种方式提取一级分类
                    let firstLevelCategory = '';

                    if (result.bookName.includes(' > ')) {
                        // 如果包含 ' > '，取第一部分
                        firstLevelCategory = result.bookName.split(' > ')[0];
                    } else if (result.bookName.includes('/')) {
                        // 如果包含 '/'，取第一部分
                        firstLevelCategory = result.bookName.split('/')[0];
                    } else {
                        // 否则直接使用整个bookName
                        firstLevelCategory = result.bookName;
                    }

                    // 去除可能的空格
                    firstLevelCategory = firstLevelCategory.trim();

                    const matches = firstLevelCategory === selectedCategory;

                    // 调试：输出匹配和不匹配的样本
                    if (matches && index < 3) {
                        console.log(`✅ 匹配 ${index + 1}: bookName="${result.bookName}", 提取="${firstLevelCategory}", 目标="${selectedCategory}"`);
                    } else if (!matches && firstLevelCategory === selectedCategory && index < 3) {
                        console.log(`❌ 不匹配 ${index + 1}: bookName="${result.bookName}", 提取="${firstLevelCategory}", 目标="${selectedCategory}"`);
                    }

                    return matches;
                });

                console.log(`筛选完成，匹配到 ${filteredResults.length} 个结果`);
            }

            console.log(`筛选后结果数量: ${filteredResults.length}`);

            // 重新显示筛选后的结果，重置分页状态
            window.filteredResults = filteredResults;
            window.currentSearchTerm = window.currentSearchTerm || '';

            console.log(`切换到分类"${selectedCategory}"，准备显示 ${filteredResults.length} 个结果`);

            // 重新调用displaySearchResults，完全重置分页状态，传入当前选中的分类
            displaySearchResults(filteredResults, window.currentSearchTerm, selectedCategory);
        }



        // 从bob.md文件获取分类名称
        async function getCategoriesFromBob() {
            try {
                const response = await fetch('./books/bob.md');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const content = await response.text();

                // 解析markdown链接，提取分类名称
                const categories = [];
                const lines = content.split('\n');

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    // 匹配格式：- [分类名称](./路径/toc.md)
                    const match = trimmedLine.match(/^-\s*\[([^\]]+)\]\(\.\/[^)]+\)$/);
                    if (match) {
                        categories.push(match[1]);
                    }
                }

                console.log('从bob.md获取的分类:', categories);
                return categories;
            } catch (error) {
                console.error('获取bob.md分类失败:', error);
                // 返回默认分类
                return ['圣经研读', '生命读经', '属灵书报', '倪著文集', '李著文集', '特会纲目', '诗歌合辑'];
            }
        }

        // 高亮搜索词
        function highlightSearchTerm(text, searchTerm) {
            if (!text) return '';
            const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
            return text.replace(regex, match => `<span class="search-highlight">${match}</span>`);
        }

        // 分页+懒加载显示搜索结果
        function displaySearchResults(results, searchTerm, activeCategory = 'all') {
            const searchResults = document.getElementById('search-results');
            const floatingResultsContainer = document.getElementById('floating-results-container');

            // 移除加载状态样式
            searchResults.classList.remove('loading-state');

            // 始终显示头部，即使没有结果
            searchResults.innerHTML = generateHeaderHTML(activeCategory, results.length);

            if (results.length === 0) {
                console.log('未找到匹配结果');
                const categoryText = activeCategory === 'all' ? '' : ` (分类: ${activeCategory})`;

                // 显示无结果信息，但保留头部
                const resultsContent = document.getElementById('results-content');
                resultsContent.innerHTML = `<div class="no-results">未找到匹配结果${categoryText}</div>`;

                // 隐藏分页导航
                const paginationContainer = document.getElementById('pagination-container');
                paginationContainer.innerHTML = '';

                // 绑定头部事件
                bindHeaderEvents();
                return;
            }

            console.log(`开始分页显示 ${results.length} 个搜索结果，当前分类: ${activeCategory}`);

            // 设置当前显示的结果数组（这是关键！）
            window.currentDisplayResults = results;
            // 保存当前选中的分类
            window.currentActiveCategory = activeCategory;

            // 分页配置
            const RESULTS_PER_PAGE = 100;  // 每页显示15个结果
            let currentPage = 0;
            let isLoading = false;

            // 生成页码导航HTML
            function generatePaginationHTML(currentPage, totalPages, totalResults) {
                if (totalPages <= 1) return '';

                let paginationHTML = '<div class="pagination">';

                // 显示结果统计
                const startResult = currentPage * RESULTS_PER_PAGE + 1;
                const endResult = Math.min((currentPage + 1) * RESULTS_PER_PAGE, totalResults);
                paginationHTML += `<div class="pagination-info">显示 ${startResult}-${endResult} / 共 ${totalResults} 个结果</div>`;

                paginationHTML += '<div class="pagination-buttons">';

                // 上一页按钮
                if (currentPage > 0) {
                    paginationHTML += `<button class="pagination-btn" data-page="${currentPage - 1}">‹‹</button>`;
                }

                // 页码按钮 - 根据屏幕大小调整显示数量
                const isMobile = window.innerWidth <= 768;
                const maxVisiblePages = isMobile ? 3 : 5;
                let startPage = Math.max(0, currentPage - Math.floor(maxVisiblePages / 2));
                let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);

                // 调整起始页，确保显示足够的页码
                if (endPage - startPage < maxVisiblePages - 1) {
                    startPage = Math.max(0, endPage - maxVisiblePages + 1);
                }

                // 第一页
                if (startPage > 0) {
                    paginationHTML += `<button class="pagination-btn" data-page="0">1</button>`;
                    if (startPage > 1) {
                        paginationHTML += '<span class="pagination-ellipsis">...</span>';
                    }
                }

                // 中间页码
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === currentPage ? 'active' : '';
                    paginationHTML += `<button class="pagination-btn ${isActive}" data-page="${i}">${i + 1}</button>`;
                }

                // 最后一页
                if (endPage < totalPages - 1) {
                    if (endPage < totalPages - 2) {
                        paginationHTML += '<span class="pagination-ellipsis">...</span>';
                    }
                    paginationHTML += `<button class="pagination-btn" data-page="${totalPages - 1}">${totalPages}</button>`;
                }

                // 下一页按钮
                if (currentPage < totalPages - 1) {
                    paginationHTML += `<button class="pagination-btn" data-page="${currentPage + 1}">››</button>`;
                }

                paginationHTML += '</div></div>';
                return paginationHTML;
            }

            // 生成头部HTML
            function generateHeaderHTML(activeCategory = 'all', resultsCount = 0) {
                // 始终使用bob.md中的分类
                const firstLevelCategories = window.bobCategories || ['圣经研读', '生命读经', '属灵书报', '倪著文集', '李著文集', '特会纲目', '诗歌合辑'];
                console.log('使用bob.md分类:', firstLevelCategories);

                return `
          <div class="search-results-header">
            <div class="results-tabs">
              <div class="tab-item ${activeCategory === 'all' ? 'active' : ''}" data-category="all">全部</div>
              ${firstLevelCategories.map(cat => `<div class="tab-item ${activeCategory === cat ? 'active' : ''}" data-category="${cat}">${cat}</div>`).join('')}
            </div>
            <div class="results-info-line">
              <div class="results-count">共 ${resultsCount} 个</div>
              <div class="results-controls">
                <button class="sort-by-matches-btn" id="sort-by-matches-btn" title="按匹配数排序">📊</button>
                <button class="toggle-all-btn" id="toggle-all-btn" title="展开全部">📂</button>
                <button class="close-results-btn" id="close-results-btn">×</button>
              </div>
            </div>
          </div>
          <div class="results-content" id="results-content"></div>
          <div class="pagination-container" id="pagination-container"></div>
          <style>
            @keyframes loading {
              0%, 100% { transform: translateX(-100%); }
              50% { transform: translateX(100%); }
            }
            .results-filter {
              margin: 10px 0;
            }
            .results-filter label {
              margin-right: 8px;
              font-weight: bold;
            }
            .results-filter select {
              padding: 4px 8px;
              border: 1px solid #ddd;
              border-radius: 4px;
              background: white;
            }
            .sort-by-matches-btn {
              background: #f0f0f0;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 6px 8px;
              cursor: pointer;
              font-size: 14px;
              margin-right: 5px;
              transition: all 0.2s ease;
            }
            .sort-by-matches-btn:hover {
              background: #e0e0e0;
              border-color: #bbb;
            }
            .sort-by-matches-btn.active {
              color: white;
              border-color: #0056b3;
            }
            .sort-by-matches-btn.desc {
              background: #007bff; /* 蓝色表示降序 */
            }
            .sort-by-matches-btn.asc {
              background: #28a745; /* 绿色表示升序 */
            }
            .toggle-all-btn {
              background: #f0f0f0;
              border: 1px solid #ddd;
              border-radius: 4px;
              padding: 6px 8px;
              cursor: pointer;
              font-size: 14px;
              margin-right: 5px;
              transition: all 0.2s ease;
            }
            .toggle-all-btn:hover {
              background: #e0e0e0;
              border-color: #bbb;
            }
            .toggle-all-btn.expanded {
              background: #17a2b8; /* 青色表示已展开状态 */
              color: white;
              border-color: #138496;
            }
          </style>
        `;
            }

            // 生成单页结果HTML
            function generatePageHTML(pageResults, startIndex) {
                let html = '';

                pageResults.forEach((result, index) => {
                    const globalIndex = startIndex + index;
                    const documentId = `doc-${globalIndex}`;

                    html += `
            <div class="search-result-item" data-book-path="${result.bookPath}" data-chapter-name="${result.chapterName}">
              <div class="search-result-header">
                <div class="search-result-title" data-document-id="${documentId}">
                  ${result.bookName} - ${result.title}
                  <span class="match-count">(${result.matchCount}处匹配)</span>
                  <span class="toggle-icon collapsed">▶</span>
                </div>
              </div>
              <div class="search-result-contexts" id="${documentId}" style="display: none;">`;

                    // 添加匹配的上下文
                    result.contexts.forEach((ctx, j) => {
                        const matchPosition = ctx.position;
                        const lineNumber = ctx.lineNumber || 0;
                        const matchTypeClass = ctx.type === 'title' ? 'title-match' :
                            ctx.type === 'footnote' ? 'footnote-match' :
                                ctx.type === 'verse' ? 'verse-match' : 'content-match';

                        // 为脚注匹配添加额外的数据属性
                        const footnoteAttributes = ctx.type === 'footnote' ?
                            `data-footnote-key="${ctx.footnoteKey || ''}" data-section-number="${ctx.sectionNumber || ''}"` : '';

                        // 为经文匹配添加额外的数据属性
                        const verseAttributes = ctx.type === 'verse' ?
                            `data-section-number="${ctx.verseNumber || ''}"` : '';

                        html += `
              <div class="context-item ${matchTypeClass}"
                   data-book-path="${result.bookPath}"
                   data-chapter-name="${result.chapterName}"
                   data-match-position="${matchPosition}"
                   data-line-number="${lineNumber}"
                   data-match-type="${ctx.type}"
                   ${footnoteAttributes}
                   ${verseAttributes}>
                <div class="context-number">${j + 1}.</div>
                <div class="context-text">${highlightSearchTerm(ctx.context, searchTerm)}</div>
              </div>`;
                    });

                    // 如果有更多匹配未显示，添加提示
                    if (result.matchCount > result.contexts.length) {
                        html += `<div class="more-matches">还有 ${result.matchCount - result.contexts.length} 处匹配...</div>`;
                    }

                    html += `</div></div>`;
                });

                return html;
            }

            // 显示指定页面
            function showPage(pageNumber) {
                // 使用当前显示的结果数组
                const currentResults = window.currentDisplayResults || results;
                const totalPages = Math.ceil(currentResults.length / RESULTS_PER_PAGE);

                // 验证页码
                if (pageNumber < 0 || pageNumber >= totalPages) return;

                const start = pageNumber * RESULTS_PER_PAGE;
                const end = Math.min(start + RESULTS_PER_PAGE, currentResults.length);

                // 生成当前页的结果
                const pageResults = currentResults.slice(start, end);
                const pageHTML = generatePageHTML(pageResults, start);

                // 更新页面内容
                const resultsContent = document.getElementById('results-content');
                resultsContent.innerHTML = pageHTML;

                // 更新页码导航
                const paginationContainer = document.getElementById('pagination-container');
                paginationContainer.innerHTML = generatePaginationHTML(pageNumber, totalPages, currentResults.length);

                // 更新当前页码
                currentPage = pageNumber;

                console.log(`显示第 ${pageNumber + 1} 页，结果 ${start + 1}-${end} / 共 ${currentResults.length} 个`);

                // 绑定事件
                bindPageEvents();
                bindPaginationEvents();

                // 滚动到顶部
                const searchResults = document.getElementById('search-results');
                if (searchResults) {
                    searchResults.scrollTop = 0;
                }
            }

            // 绑定页码导航事件
            function bindPaginationEvents() {
                const paginationBtns = document.querySelectorAll('.pagination-btn');
                paginationBtns.forEach(btn => {
                    if (!btn.hasAttribute('data-bound')) {
                        btn.setAttribute('data-bound', 'true');
                        btn.addEventListener('click', function () {
                            const targetPage = parseInt(this.getAttribute('data-page'));
                            showPage(targetPage);
                        });
                    }
                });
            }

            // 移除加载状态样式
            searchResults.classList.remove('loading-state');

            // 初始化显示
            searchResults.innerHTML = generateHeaderHTML(activeCategory, results.length);

            // 设置当前显示的结果
            window.currentDisplayResults = results;

            // 显示第一页
            showPage(0);

            // 全局变量，用于分类筛选
            // 只在初始搜索时设置 allSearchResults，筛选时不要修改
            if (activeCategory === 'all') {
                window.allSearchResults = results;
                console.log('设置 window.allSearchResults，长度:', results.length);
            } else {
                console.log('筛选操作，保持 window.allSearchResults 不变，长度:', window.allSearchResults ? window.allSearchResults.length : 0);
            }
            window.currentSearchTerm = searchTerm;
            window.filteredResults = results;

            // 绑定头部事件的函数（分类标签和关闭按钮）
            function bindHeaderEvents() {
                // 绑定tab切换事件
                const resultTabItems = document.querySelectorAll('.results-tabs .tab-item');
                resultTabItems.forEach(tab => {
                    if (!tab.hasAttribute('data-bound')) {
                        tab.setAttribute('data-bound', 'true');
                        tab.addEventListener('click', function () {
                            // 获取选中的分类
                            const selectedCategory = this.getAttribute('data-category');
                            console.log('切换到分类:', selectedCategory);

                            // 执行分类筛选（这会重新生成整个界面，包括正确的active状态）
                            filterResultsByCategory(selectedCategory);
                        });
                    }
                });

                // 为关闭按钮添加事件（只绑定一次）
                const closeResultsBtn = document.getElementById('close-results-btn');
                if (closeResultsBtn && !closeResultsBtn.hasAttribute('data-bound')) {
                    closeResultsBtn.setAttribute('data-bound', 'true');
                    closeResultsBtn.addEventListener('click', function () {
                        floatingResultsContainer.classList.remove('active');
                        const floatingSearchContainer = document.getElementById('floating-search-container');
                        if (!floatingSearchContainer || !floatingSearchContainer.classList.contains('active')) {
                            document.documentElement.classList.remove('modal-open');
                        }
                    });
                }

                // 为按匹配数排序按钮添加事件（只绑定一次）
                const sortByMatchesBtn = document.getElementById('sort-by-matches-btn');
                if (sortByMatchesBtn && !sortByMatchesBtn.hasAttribute('data-bound')) {
                    sortByMatchesBtn.setAttribute('data-bound', 'true');

                    // 初始化排序状态（如果没有设置）
                    if (window.sortState === undefined) {
                        window.sortState = 'original'; // 'original', 'desc', 'asc'
                    }

                    // 恢复按钮状态
                    updateSortButtonState(sortByMatchesBtn, window.sortState);

                    sortByMatchesBtn.addEventListener('click', function () {
                        console.log('🔄 切换排序状态');

                        // 循环切换排序状态：原始 → 降序 → 升序 → 原始
                        switch (window.sortState) {
                            case 'original':
                                window.sortState = 'desc';
                                break;
                            case 'desc':
                                window.sortState = 'asc';
                                break;
                            case 'asc':
                                window.sortState = 'original';
                                break;
                            default:
                                window.sortState = 'desc';
                        }

                        // 更新按钮状态
                        updateSortButtonState(this, window.sortState);

                        // 执行排序
                        applySorting(window.sortState);
                    });
                }

                // 更新排序按钮状态的函数
                function updateSortButtonState(button, state) {
                    button.classList.remove('active', 'desc', 'asc');

                    switch (state) {
                        case 'original':
                            button.title = '按匹配数排序（降序）';
                            button.textContent = '📊';
                            break;
                        case 'desc':
                            button.classList.add('active', 'desc');
                            button.title = '按匹配数排序（升序）';
                            button.textContent = '📊↓';
                            break;
                        case 'asc':
                            button.classList.add('active', 'asc');
                            button.title = '恢复原始顺序';
                            button.textContent = '📊↑';
                            break;
                    }
                }

                // 应用排序的函数
                function applySorting(state) {
                    const currentResults = window.currentDisplayResults || [];

                    // 在第一次排序时保存原始顺序
                    if (!window.originalSearchResults) {
                        window.originalSearchResults = [...currentResults];
                        console.log('💾 保存原始搜索结果顺序，长度:', window.originalSearchResults.length);
                    }

                    let sortedResults;

                    switch (state) {
                        case 'original':
                            console.log('📋 恢复原始顺序');
                            sortedResults = window.originalSearchResults || currentResults;
                            break;
                        case 'desc':
                            console.log('📊↓ 按匹配数降序排序');
                            sortedResults = [...currentResults].sort((a, b) => {
                                const matchCountA = a.matchCount || a.contexts?.length || 0;
                                const matchCountB = b.matchCount || b.contexts?.length || 0;
                                return matchCountB - matchCountA; // 降序：多到少
                            });
                            break;
                        case 'asc':
                            console.log('📊↑ 按匹配数升序排序');
                            sortedResults = [...currentResults].sort((a, b) => {
                                const matchCountA = a.matchCount || a.contexts?.length || 0;
                                const matchCountB = b.matchCount || b.contexts?.length || 0;
                                return matchCountA - matchCountB; // 升序：少到多
                            });
                            break;
                        default:
                            sortedResults = currentResults;
                    }

                    console.log(`📊 排序完成：${sortedResults.length} 个结果，状态：${state}`);
                    displaySearchResults(sortedResults, window.currentSearchTerm, window.currentActiveCategory);
                }
            }

            // 绑定页面事件的函数
            function bindPageEvents() {
                // 先绑定头部事件
                bindHeaderEvents();

                // 为全局展开/折叠切换按钮添加事件（只绑定一次）
                const toggleAllBtn = document.getElementById('toggle-all-btn');

                if (toggleAllBtn && !toggleAllBtn.hasAttribute('data-bound')) {
                    toggleAllBtn.setAttribute('data-bound', 'true');

                    // 初始化按钮状态（默认为折叠状态）
                    window.allExpanded = false;
                    updateToggleAllButtonState(toggleAllBtn, window.allExpanded);

                    toggleAllBtn.addEventListener('click', function () {
                        const allContexts = document.querySelectorAll('.search-result-contexts');

                        // 切换状态
                        window.allExpanded = !window.allExpanded;

                        if (window.allExpanded) {
                            // 展开全部
                            console.log('📂 展开全部搜索结果');
                            allContexts.forEach(context => {
                                context.style.display = 'block';
                                const titleElement = document.querySelector(`.search-result-title[data-document-id="${context.id}"]`);
                                if (titleElement) {
                                    const toggleIcon = titleElement.querySelector('.toggle-icon');
                                    if (toggleIcon) {
                                        toggleIcon.textContent = '▼';
                                        toggleIcon.classList.add('expanded');
                                        toggleIcon.classList.remove('collapsed');
                                    }
                                }
                            });
                        } else {
                            // 折叠全部
                            console.log('📁 折叠全部搜索结果');
                            allContexts.forEach(context => {
                                context.style.display = 'none';
                                const titleElement = document.querySelector(`.search-result-title[data-document-id="${context.id}"]`);
                                if (titleElement) {
                                    const toggleIcon = titleElement.querySelector('.toggle-icon');
                                    if (toggleIcon) {
                                        toggleIcon.textContent = '▶';
                                        toggleIcon.classList.add('collapsed');
                                        toggleIcon.classList.remove('expanded');
                                    }
                                }
                            });
                        }

                        // 更新按钮状态
                        updateToggleAllButtonState(this, window.allExpanded);
                    });
                }

                // 更新切换按钮状态的函数
                function updateToggleAllButtonState(button, isExpanded) {
                    if (isExpanded) {
                        button.classList.add('expanded');
                        button.textContent = '📁';
                        button.title = '折叠全部';
                    } else {
                        button.classList.remove('expanded');
                        button.textContent = '📂';
                        button.title = '展开全部';
                    }
                }

                // 使用事件委托为结果项绑定事件
                const resultsContent = document.getElementById('results-content');
                if (resultsContent && !resultsContent.hasAttribute('data-bound')) {
                    resultsContent.setAttribute('data-bound', 'true');

                    resultsContent.addEventListener('click', function (e) {
                        const target = e.target;

                        // 处理标题点击 - 展开/折叠
                        if (target.classList.contains('search-result-title') || target.closest('.search-result-title')) {
                            const titleElement = target.classList.contains('search-result-title') ? target : target.closest('.search-result-title');
                            const documentId = titleElement.getAttribute('data-document-id');
                            const contextContainer = document.getElementById(documentId);
                            const toggleIcon = titleElement.querySelector('.toggle-icon');

                            if (contextContainer) {
                                if (contextContainer.style.display === 'none') {
                                    contextContainer.style.display = 'block';
                                    if (toggleIcon) {
                                        toggleIcon.textContent = '▼';
                                        toggleIcon.classList.add('expanded');
                                        toggleIcon.classList.remove('collapsed');
                                    }
                                } else {
                                    contextContainer.style.display = 'none';
                                    if (toggleIcon) {
                                        toggleIcon.textContent = '▶';
                                        toggleIcon.classList.add('collapsed');
                                        toggleIcon.classList.remove('expanded');
                                    }
                                }
                            }
                            e.stopPropagation();
                        }

                        // 处理上下文项点击 - 跳转到章节
                        else if (target.classList.contains('context-item') || target.closest('.context-item')) {
                            const contextItem = target.classList.contains('context-item') ? target : target.closest('.context-item');
                            const bookPath = contextItem.getAttribute('data-book-path');
                            const chapterName = contextItem.getAttribute('data-chapter-name');
                            const matchPosition = contextItem.getAttribute('data-match-position');
                            const lineNumber = contextItem.getAttribute('data-line-number');
                            const matchType = contextItem.getAttribute('data-match-type');
                            const footnoteKey = contextItem.getAttribute('data-footnote-key');
                            const sectionNumber = contextItem.getAttribute('data-section-number');

                            console.log(`点击匹配项: ${chapterName}, 路径: ${bookPath}, 位置: ${matchPosition}, 行号: ${lineNumber}, 类型: ${matchType}, 脚注键: ${footnoteKey}, 节号: ${sectionNumber}`);

                            // 生成唯一的锚点ID
                            const anchorId = `match-${matchPosition}`;

                            // 如果是脚注匹配，直接弹出脚注窗口，不关闭搜索结果
                            if (matchType === 'footnote' && footnoteKey) {
                                console.log(`脚注匹配，直接弹出脚注窗口: 脚注键="${footnoteKey}"`);

                                // 从搜索索引中获取完整的脚注内容
                                let footnoteContent = '';
                                let foundFootnote = false;

                                console.log(`查找脚注: 键="${footnoteKey}", 节号="${sectionNumber}", 路径="${bookPath}/${chapterName}"`);

                                // 首先尝试根据节号精确查找脚注
                                for (const indexItem of searchIndex) {
                                    const itemBookPath = indexItem.bookPath || indexItem.bp || '';
                                    const itemChapterName = indexItem.chapterName || indexItem.cn || '';
                                    const itemFootnotes = indexItem.footnotes || indexItem.f || {};

                                    if (itemBookPath === bookPath &&
                                        itemChapterName === chapterName &&
                                        itemFootnotes && Object.keys(itemFootnotes).length > 0) {

                                        // 查找匹配节号的脚注
                                        for (const [key, data] of Object.entries(itemFootnotes)) {
                                            let footnoteData = data;
                                            let originalKey = key;
                                            let footnoteSectionNumber = '';

                                            // 处理脚注数据结构
                                            if (typeof data === 'object' && data.content) {
                                                footnoteData = data;
                                                originalKey = data.originalKey || key;
                                                footnoteSectionNumber = data.sectionNumber || '';
                                            }

                                            // 检查脚注键和节号是否匹配
                                            if (originalKey === footnoteKey && footnoteSectionNumber === sectionNumber) {
                                                if (typeof footnoteData === 'string') {
                                                    footnoteContent = footnoteData;
                                                } else {
                                                    footnoteContent = footnoteData.content;
                                                }
                                                foundFootnote = true;
                                                console.log(`✅ 找到精确匹配的脚注: 键="${originalKey}", 节号="${footnoteSectionNumber}"`);
                                                break;
                                            }
                                        }
                                        if (foundFootnote) break;
                                    }
                                }

                                // 如果没找到精确匹配，尝试只匹配脚注键（作为备选）
                                if (!foundFootnote) {
                                    console.log('⚠️ 未找到精确匹配，尝试只匹配脚注键');
                                    for (const indexItem of searchIndex) {
                                        const itemBookPath = indexItem.bookPath || indexItem.bp || '';
                                        const itemChapterName = indexItem.chapterName || indexItem.cn || '';
                                        const itemFootnotes = indexItem.footnotes || indexItem.f || {};

                                        if (itemBookPath === bookPath &&
                                            itemChapterName === chapterName &&
                                            itemFootnotes && Object.keys(itemFootnotes).length > 0) {

                                            for (const [key, data] of Object.entries(itemFootnotes)) {
                                                const originalKey = (typeof data === 'object' && data.originalKey) ? data.originalKey : key;
                                                if (originalKey === footnoteKey) {
                                                    if (typeof data === 'string') {
                                                        footnoteContent = data;
                                                    } else {
                                                        footnoteContent = data.content;
                                                    }
                                                    foundFootnote = true;
                                                    console.log(`⚠️ 找到脚注键匹配: 键="${originalKey}"`);
                                                    break;
                                                }
                                            }
                                            if (foundFootnote) break;
                                        }
                                    }
                                }

                                // 如果没有找到完整内容，使用显示的上下文内容
                                if (!footnoteContent) {
                                    footnoteContent = contextItem.querySelector('.context-text').textContent;
                                }

                                // 从搜索结果中获取书籍信息
                                let bookName = '';
                                const bookPathParts = bookPath.split('/');
                                if (bookPathParts.length >= 2) {
                                    bookName = bookPathParts[bookPathParts.length - 2]; // 获取书籍名（倒数第二个部分）
                                }

                                // 创建脚注弹窗
                                showDirectFootnotePopup(footnoteKey, footnoteContent, searchTerm, bookName, chapterName, sectionNumber);
                            }
                            // 如果是经文匹配，直接弹出经文窗口
                            else if (matchType === 'verse') {
                                console.log(`经文匹配，直接弹出经文窗口: 章节="${chapterName}"`);

                                // 从搜索索引中获取经文数据
                                let verseData = null;
                                for (const indexItem of searchIndex) {
                                    const itemBookPath = indexItem.bookPath || indexItem.bp || '';
                                    const itemChapterName = indexItem.chapterName || indexItem.cn || '';

                                    if (indexItem.type === 'verse' &&
                                        itemBookPath === bookPath &&
                                        itemChapterName === chapterName &&
                                        indexItem.verseNumber === sectionNumber) {
                                        verseData = indexItem;
                                        break;
                                    }
                                }

                                // 如果没有找到经文数据，从上下文中构建
                                if (!verseData) {
                                    const contextText = contextItem.querySelector('.context-text').textContent;
                                    const bookPathParts = bookPath.split('/');
                                    const actualBookName = bookPathParts.length >= 2 ? bookPathParts[bookPathParts.length - 2] : '';

                                    verseData = {
                                        title: `${actualBookName}${chapterName}:${sectionNumber}`,
                                        content: contextText,
                                        verseNumber: sectionNumber,
                                        bookName: "圣经研读",
                                        actualBookName: actualBookName,
                                        chapterName: chapterName,
                                        type: 'verse'
                                    };
                                }

                                // 创建经文弹窗
                                showVersePopup(verseData, searchTerm);
                            }
                            // 如果是圣经研读的内容匹配，也显示弹窗而不是跳转
                            else if (bookPath && bookPath.includes('圣经研读') && matchType === 'content') {
                                console.log(`🎯 圣经研读内容匹配，显示弹窗: 章节="${chapterName}"`);

                                // 从上下文中获取文本内容
                                const contextText = contextItem.querySelector('.context-text').textContent;
                                const bookPathParts = bookPath.split('/');
                                const actualBookName = bookPathParts.length >= 2 ? bookPathParts[bookPathParts.length - 2] : '';

                                // 创建临时的经文数据
                                const verseData = {
                                    title: `${actualBookName}${chapterName}`,
                                    content: contextText,
                                    verseNumber: sectionNumber || '1', // 如果没有节号，默认为1
                                    bookName: "圣经研读",
                                    actualBookName: actualBookName,
                                    chapterName: chapterName,
                                    type: 'verse'
                                };

                                // 创建经文弹窗
                                showVersePopup(verseData, searchTerm);
                            }
                            // 统一使用全屏弹窗处理所有其他类型的匹配
                            else {
                                console.log(`🎯 使用全屏弹窗显示: bookPath="${bookPath}", matchType="${matchType}", matchPosition="${matchPosition}"`);

                                // 使用全屏弹窗加载章节，传递点击的元素用于提取标题和匹配位置
                                loadChapterInModal(bookPath, chapterName, searchTerm, '', contextItem, parseInt(matchPosition) || 1);
                            }
                            e.stopPropagation();
                        }
                    });
                }
            }

            console.log('分页搜索结果显示完成');
        }



        // 在章节内容中高亮搜索词（智能处理脚注标记）
        function highlightContentSearchTerm(searchTerm, anchorId = '', lineNumber = 0) {
            if (!searchTerm) return;

            console.log(`对章节内容中的搜索词 "${searchTerm}" 进行高亮处理，锚点: ${anchorId}, 行号: ${lineNumber}`);

            // 获取内容元素
            const contentDiv = document.getElementById("content");
            if (!contentDiv) return;

            // 创建TreeWalker来遍历文本节点
            const walker = document.createTreeWalker(
                contentDiv,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: function (node) {
                        // 跳过script标签中的内容
                        if (node.parentNode.tagName === 'SCRIPT') {
                            return NodeFilter.FILTER_REJECT;
                        }

                        // 智能匹配：检查原文本或去除脚注标记后的文本是否包含搜索词
                        const originalText = node.textContent.toLowerCase();
                        const cleanText = originalText.replace(/\[\^[^\]]+\]/g, ''); // 去除脚注标记

                        if (originalText.includes(searchTerm.toLowerCase()) ||
                            cleanText.includes(searchTerm.toLowerCase())) {
                            return NodeFilter.FILTER_ACCEPT;
                        }
                        return NodeFilter.FILTER_SKIP;
                    }
                }
            );

            // 收集需要替换的节点
            const nodesToReplace = [];
            let currentNode;
            let currentHighlightId = 0;

            while (currentNode = walker.nextNode()) {
                nodesToReplace.push({
                    node: currentNode,
                    highlightId: ++currentHighlightId  // 为每个匹配分配唯一ID
                });
            }

            // 智能替换节点内容，处理脚注标记
            nodesToReplace.forEach(nodeInfo => {
                const node = nodeInfo.node;
                const highlightId = nodeInfo.highlightId;

                // 为每个匹配生成唯一ID
                const matchId = `match-${highlightId}`;

                const originalText = node.textContent;
                let newContent = originalText;

                // 智能高亮：使用更简单可靠的方法
                const searchTermLower = searchTerm.toLowerCase();
                const originalTextLower = originalText.toLowerCase();

                // 1. 首先尝试直接匹配
                if (originalTextLower.includes(searchTermLower)) {
                    const directRegex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                    newContent = originalText.replace(directRegex, `<span id="${matchId}" class="search-highlight" data-match-id="${highlightId}">$1</span>`);
                } else {
                    // 2. 如果直接匹配失败，尝试智能匹配（处理脚注标记）
                    // 创建一个映射，记录原文中每个字符对应的清理后文本的位置
                    const cleanText = originalText.replace(/\[\^[^\]]+\]/g, '');
                    const cleanTextLower = cleanText.toLowerCase();

                    if (cleanTextLower.includes(searchTermLower)) {
                        // 使用正则表达式分割原文，保留脚注标记
                        const parts = originalText.split(/(\[\^[^\]]+\])/);
                        let reconstructed = '';
                        let cleanPosition = 0;
                        let found = false;

                        for (let i = 0; i < parts.length; i++) {
                            const part = parts[i];

                            if (part.match(/^\[\^[^\]]+\]$/)) {
                                // 这是脚注标记，直接添加
                                reconstructed += part;
                            } else {
                                // 这是普通文本，检查是否包含搜索词
                                const partLower = part.toLowerCase();
                                const searchIndex = partLower.indexOf(searchTermLower);

                                if (searchIndex !== -1 && !found) {
                                    // 在这部分找到了搜索词
                                    const before = part.substring(0, searchIndex);
                                    const match = part.substring(searchIndex, searchIndex + searchTerm.length);
                                    const after = part.substring(searchIndex + searchTerm.length);

                                    reconstructed += before + `<span id="${matchId}" class="search-highlight" data-match-id="${highlightId}">${match}</span>` + after;
                                    found = true;
                                } else {
                                    reconstructed += part;
                                }
                                cleanPosition += part.length;
                            }
                        }

                        if (found) {
                            newContent = reconstructed;
                        }
                    }
                }

                // 创建临时元素来包含替换后的HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = newContent;

                // 创建文档片段来替换原节点
                const fragment = document.createDocumentFragment();
                while (tempDiv.firstChild) {
                    fragment.appendChild(tempDiv.firstChild);
                }

                // 替换原节点
                node.parentNode.replaceChild(fragment, node);
            });

            // 如果找到了匹配项，添加一个滚动到第一个高亮的功能
            const highlights = contentDiv.querySelectorAll('.search-highlight');
            if (highlights.length > 0) {
                console.log(`在章节内容中找到 ${highlights.length} 个匹配项`);

                // 移除任何已存在的搜索导航栏
                const existingSearchInfo = document.getElementById('search-nav-floating');
                if (existingSearchInfo) {
                    existingSearchInfo.remove();
                }

                // 创建悬浮的导航面板
                const searchInfo = document.createElement('div');
                searchInfo.className = 'search-info floating';
                searchInfo.id = 'search-nav-floating';
                searchInfo.innerHTML = `
            <div class="highlight-navigation">
              <span class="current-highlight">1</span>/<span class="total-highlights">${highlights.length}</span>
              <button class="nav-highlight-btn prev-btn" id="prev-highlight-btn">上一个</button>
              <button class="nav-highlight-btn next-btn" id="next-highlight-btn">下一个</button>
              <button class="clear-highlight-btn" id="clear-highlight-btn">清除高亮</button>
            </div>
          </div>
        `;

                // 添加到页面
                document.body.appendChild(searchInfo);

                // 确定要滚动到哪个匹配项
                let targetHighlight = null;

                if (anchorId) {
                    // 如果指定了锚点ID，则滚动到对应的匹配项
                    targetHighlight = document.getElementById(anchorId);
                }

                if (!targetHighlight && lineNumber > 0) {
                    // 如果没有找到锚点但有行号，则尝试找到最接近行号的匹配项
                    // 这里使用简单估算，因为我们没有精确的行号信息
                    const contentText = contentDiv.textContent;
                    const contentLines = contentText.split('\n');

                    let currentLine = 0;
                    let bestMatchDistance = Number.MAX_VALUE;
                    let bestMatchElement = null;

                    // 检查每个高亮元素，找到最接近指定行号的元素
                    highlights.forEach(highlight => {
                        // 计算高亮元素到文档开头的文本量
                        const range = document.createRange();
                        range.setStart(contentDiv, 0);
                        range.setEnd(highlight, 0);
                        const textBefore = range.toString();

                        // 根据文本计算行号
                        const linesBefore = textBefore.split('\n').length;
                        const distance = Math.abs(linesBefore - lineNumber);

                        if (distance < bestMatchDistance) {
                            bestMatchDistance = distance;
                            bestMatchElement = highlight;
                        }
                    });

                    targetHighlight = bestMatchElement;
                }

                // 如果没有特定目标，则使用第一个匹配项
                if (!targetHighlight) {
                    targetHighlight = highlights[0];
                }

                // 记录当前激活的高亮索引
                window.currentHighlightIndex = Array.from(highlights).indexOf(targetHighlight);

                // 确保索引在有效范围内
                if (window.currentHighlightIndex < 0 || window.currentHighlightIndex >= highlights.length) {
                    window.currentHighlightIndex = 0;
                    // 如果索引无效，使用第一个高亮作为目标
                    targetHighlight = highlights[0];
                }

                // 更新当前高亮计数器
                const currentHighlightElement = document.querySelector('.current-highlight');
                if (currentHighlightElement) {
                    currentHighlightElement.textContent = window.currentHighlightIndex + 1;
                }

                console.log(`初始化高亮索引: ${window.currentHighlightIndex}, 总数: ${highlights.length}`);

                // 添加激活类到当前高亮
                if (targetHighlight) {
                    targetHighlight.classList.add('active-highlight');
                }

                // 滚动到目标匹配项
                setTimeout(() => {
                    if (targetHighlight) {
                        targetHighlight.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 300);

                // 添加导航按钮的事件监听器（避免重复绑定）
                setTimeout(() => {
                    const prevBtn = document.getElementById('prev-highlight-btn');
                    const nextBtn = document.getElementById('next-highlight-btn');
                    const clearBtn = document.getElementById('clear-highlight-btn');

                    // 移除旧的事件监听器，避免重复绑定
                    if (prevBtn) {
                        prevBtn.replaceWith(prevBtn.cloneNode(true));
                        const newPrevBtn = document.getElementById('prev-highlight-btn');
                        newPrevBtn.addEventListener('click', function () {
                            navigateHighlight(-1);
                        });
                    }

                    if (nextBtn) {
                        nextBtn.replaceWith(nextBtn.cloneNode(true));
                        const newNextBtn = document.getElementById('next-highlight-btn');
                        newNextBtn.addEventListener('click', function () {
                            navigateHighlight(1);
                        });
                    }

                    if (clearBtn) {
                        clearBtn.replaceWith(clearBtn.cloneNode(true));
                        const newClearBtn = document.getElementById('clear-highlight-btn');
                        newClearBtn.addEventListener('click', clearContentHighlight);
                    }
                }, 500);
            } else {
                console.log('在章节内容中未找到匹配项');
            }
        }

        // 添加导航高亮项的函数
        function navigateHighlight(direction) {
            // 只查询章节内容区域的高亮，不包括搜索结果列表中的高亮
            const contentDiv = document.getElementById('content');
            const highlights = contentDiv ? contentDiv.querySelectorAll('.search-highlight') : [];
            if (!highlights.length) return;

            console.log(`导航高亮：方向=${direction}, 高亮数量=${highlights.length}`);

            // 确保当前索引已初始化且在有效范围内
            if (typeof window.currentHighlightIndex === 'undefined' ||
                window.currentHighlightIndex < 0 ||
                window.currentHighlightIndex >= highlights.length) {
                window.currentHighlightIndex = 0;
            }

            console.log(`当前高亮索引: ${window.currentHighlightIndex}`);

            // 移除当前高亮的激活状态（安全检查）
            if (highlights[window.currentHighlightIndex]) {
                highlights[window.currentHighlightIndex].classList.remove('active-highlight');
            }

            // 计算新的索引
            window.currentHighlightIndex = (window.currentHighlightIndex + direction + highlights.length) % highlights.length;

            console.log(`新的高亮索引: ${window.currentHighlightIndex}`);

            // 添加激活类到新的当前高亮
            const newActiveHighlight = highlights[window.currentHighlightIndex];
            if (newActiveHighlight) {
                newActiveHighlight.classList.add('active-highlight');
            }

            // 更新当前高亮计数器
            const currentHighlightElement = document.querySelector('.current-highlight');
            if (currentHighlightElement) {
                currentHighlightElement.textContent = window.currentHighlightIndex + 1;
            }

            // 确保元素可见
            console.log(`滚动到元素: `, newActiveHighlight);

            // 使用更可靠的滚动方法
            try {
                // 首先尝试使用scrollIntoView
                newActiveHighlight.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 添加额外的滚动逻辑作为备份
                setTimeout(() => {
                    const rect = newActiveHighlight.getBoundingClientRect();
                    const isInViewport = (
                        rect.top >= 0 &&
                        rect.left >= 0 &&
                        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
                    );

                    if (!isInViewport) {
                        console.log('元素不在视口中，使用备用滚动方法');
                        const offsetTop = newActiveHighlight.offsetTop;
                        window.scrollTo({
                            top: offsetTop - (window.innerHeight / 2),
                            behavior: 'smooth'
                        });
                    }
                }, 100);
            } catch (error) {
                console.error('滚动失败:', error);
                // 最后的备用方法
                try {
                    window.scrollTo({
                        top: newActiveHighlight.offsetTop - 100,
                        behavior: 'smooth'
                    });
                } catch (e) {
                    console.error('备用滚动也失败:', e);
                }
            }
        }

        // 清除内容高亮
        function clearContentHighlight() {
            console.log('清除章节内容中的高亮');

            // 移除搜索信息提示（包括悬浮面板）
            const searchInfo = document.querySelector('.search-info');
            if (searchInfo) {
                searchInfo.parentNode.removeChild(searchInfo);
            }

            // 移除悬浮的导航面板
            const floatingNav = document.getElementById('search-nav-floating');
            if (floatingNav) {
                floatingNav.remove();
            }

            // 移除URL中的search参数
            const params = new URLSearchParams(window.location.search);
            params.delete('search');
            params.delete('anchor');
            params.delete('line');
            const newUrl = `${window.location.pathname}?${params.toString()}`;
            window.history.replaceState(window.history.state, '', newUrl);

            // 重新加载当前章节，但不带搜索词
            const chapter = params.get('chapter') || 'bob';
            const dir = params.get('dir') || '';
            loadChapter(chapter, dir);
        }

        // ===== 阅读设置功能 =====

        // 切换设置面板显示/隐藏
        function toggleSettings() {
            const panel = document.getElementById('settings-panel');
            settingsVisible = !settingsVisible;

            if (settingsVisible) {
                // 禁止背景滚动
                document.documentElement.classList.add('modal-open');
                document.body.style.overflow = 'hidden';

                panel.style.display = 'flex';
                loadSettings();
                initSettingsEvents();

                // 添加点击外部关闭功能
                panel.addEventListener('click', handleSettingsPanelClick);
            } else {
                // 恢复背景滚动
                document.documentElement.classList.remove('modal-open');
                document.body.style.overflow = '';

                panel.style.display = 'none';

                // 移除点击事件监听器
                panel.removeEventListener('click', handleSettingsPanelClick);
            }
        }

        // 处理设置面板点击事件
        function handleSettingsPanelClick(event) {
            // 如果点击的是面板背景（不是内容区域），则关闭面板
            if (event.target === event.currentTarget) {
                toggleSettings();
            }
        }

        // 初始化设置事件监听器
        function initSettingsEvents() {
            // 背景颜色选择
            document.querySelectorAll('.color-option').forEach(option => {
                option.addEventListener('click', function () {
                    const bgType = this.dataset.bg;
                    setBackgroundColor(bgType);
                    updateColorSelection(bgType);
                });
            });

            // 字体大小滑块
            const fontSizeSlider = document.getElementById('font-size-slider');
            const fontSizeValue = document.getElementById('font-size-value');
            fontSizeSlider.addEventListener('input', function () {
                const size = parseInt(this.value);
                setFontSize(size);
                fontSizeValue.textContent = size + 'px';
            });

            // 字体类型选择
            const fontFamilySelect = document.getElementById('font-family-select');
            fontFamilySelect.addEventListener('change', function () {
                setFontFamily(this.value);
            });

            // 行间距滑块
            const lineHeightSlider = document.getElementById('line-height-slider');
            const lineHeightValue = document.getElementById('line-height-value');
            lineHeightSlider.addEventListener('input', function () {
                const height = parseFloat(this.value);
                setLineHeight(height);
                lineHeightValue.textContent = height.toString();
            });

            // 内容宽度滑块
            const contentWidthSlider = document.getElementById('content-width-slider');
            const contentWidthValue = document.getElementById('content-width-value');
            contentWidthSlider.addEventListener('input', function () {
                const width = parseInt(this.value);
                setContentWidth(width);
                contentWidthValue.textContent = width + 'px';
            });
        }

        // 设置背景颜色
        function setBackgroundColor(type) {
            const body = document.body;
            const content = document.getElementById('content');

            // 移除所有背景类
            body.classList.remove('bg-default', 'bg-warm', 'bg-dark', 'bg-green');

            // 添加新的背景类
            body.classList.add('bg-' + type);

            currentSettings.backgroundColor = type;
            saveSettings();
        }

        // 更新颜色选择状态
        function updateColorSelection(selectedType) {
            document.querySelectorAll('.color-option').forEach(option => {
                option.classList.remove('selected');
                if (option.dataset.bg === selectedType) {
                    option.classList.add('selected');
                }
            });
        }

        // 设置字体大小
        function setFontSize(size) {
            document.documentElement.style.setProperty('--content-font-size', size + 'px');
            currentSettings.fontSize = size;
            saveSettings();
        }

        // 设置字体类型
        function setFontFamily(family) {
            let fontFamily;
            switch (family) {
                case 'wenkai':
                    fontFamily = '"霞鹜文楷", sans-serif';
                    break;
                case 'huohei':
                    fontFamily = '"寒蝉活黑体 Bold", sans-serif';
                    break;
                case 'huaxin':
                    fontFamily = '"仓耳华新体", "霞鹜文楷", sans-serif';
                    break;
                case 'jinkai':
                    fontFamily = '"仓耳今楷03-W03", "霞鹜文楷", sans-serif';
                    break;
                    break;
                case 'pingyindingkai':
                    fontFamily = '"澳声通拼音鼎楷-简", "霞鹜文楷", sans-serif';
                    break;
                case 'pingxianzhensong':
                    fontFamily = '"屏显臻宋", "霞鹜文楷", sans-serif';
                    break;
                case 'xiaobiaosong':
                    fontFamily = '"方正小标宋_GBK", "霞鹜文楷", sans-serif';
                    break;
                case 'huosong':
                    fontFamily = '"ChillHuoSong_F", sans-serif';
                    break;
                case 'fangzhengkaiti':
                    fontFamily = '"FZKaiS-Extended", "霞鹜文楷", sans-serif';
                    break;
                default:
                    fontFamily = '"霞鹜文楷", sans-serif';
            }

            document.documentElement.style.setProperty('--content-font-family', fontFamily);
            currentSettings.fontFamily = family;
            saveSettings();
        }

        // 设置行间距
        function setLineHeight(height) {
            document.documentElement.style.setProperty('--content-line-height', height.toString());
            currentSettings.lineHeight = height;
            saveSettings();
        }

        // 设置内容宽度
        function setContentWidth(width) {
            document.documentElement.style.setProperty('--content-max-width', width + 'px');
            currentSettings.contentWidth = width;
            saveSettings();
        }

        // 保存设置到localStorage
        function saveSettings() {
            localStorage.setItem('readingSettings', JSON.stringify(currentSettings));
        }

        // 从localStorage加载设置
        function loadSettings() {
            const saved = localStorage.getItem('readingSettings');
            if (saved) {
                try {
                    const savedSettings = JSON.parse(saved);
                    currentSettings = { ...defaultSettings, ...savedSettings };
                } catch (error) {
                    console.error('解析保存的设置失败:', error);
                    currentSettings = { ...defaultSettings };
                }
            } else {
                currentSettings = { ...defaultSettings };
            }

            console.log('加载的设置:', currentSettings);

            // 应用设置
            applySettings();

            // 更新UI
            updateSettingsUI();
        }

        // 应用所有设置
        function applySettings() {
            setBackgroundColor(currentSettings.backgroundColor);
            setFontSize(currentSettings.fontSize);
            setFontFamily(currentSettings.fontFamily);
            setLineHeight(currentSettings.lineHeight);
            setContentWidth(currentSettings.contentWidth);
        }

        // 更新设置UI
        function updateSettingsUI() {
            // 更新颜色选择
            updateColorSelection(currentSettings.backgroundColor);

            // 更新滑块值
            const fontSizeSlider = document.getElementById('font-size-slider');
            const fontSizeValue = document.getElementById('font-size-value');
            if (fontSizeSlider && fontSizeValue) {
                fontSizeSlider.value = currentSettings.fontSize;
                fontSizeValue.textContent = currentSettings.fontSize + 'px';
            }

            // 更新字体选择
            const fontFamilySelect = document.getElementById('font-family-select');
            if (fontFamilySelect) {
                console.log('当前字体设置:', currentSettings.fontFamily);
                fontFamilySelect.value = currentSettings.fontFamily || 'wenkai';
                console.log('设置后的选择值:', fontFamilySelect.value);
            }

            // 更新行间距
            const lineHeightSlider = document.getElementById('line-height-slider');
            const lineHeightValue = document.getElementById('line-height-value');
            if (lineHeightSlider && lineHeightValue) {
                lineHeightSlider.value = currentSettings.lineHeight;
                lineHeightValue.textContent = currentSettings.lineHeight.toString();
            }

            // 更新内容宽度
            const contentWidthSlider = document.getElementById('content-width-slider');
            const contentWidthValue = document.getElementById('content-width-value');
            if (contentWidthSlider && contentWidthValue) {
                contentWidthSlider.value = currentSettings.contentWidth;
                contentWidthValue.textContent = currentSettings.contentWidth + 'px';
            }
        }

        // 重置设置
        function resetSettings() {
            if (confirm('确定要恢复默认设置吗？')) {
                currentSettings = { ...defaultSettings };
                applySettings();
                updateSettingsUI();
                saveSettings();
            }
        }

        // ===== 其他功能 =====

        document.addEventListener("DOMContentLoaded", function () {
            init();
            loadSettings(); // 页面加载时应用保存的设置
        });
    </script>
</body>

</html>