/* 默认字体 - 霞鹜文楷 */
@font-face {
  font-family: "霞鹜文楷";
  src: url("../fonts/霞鹜文楷.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "寒蝉活黑体 Bold";
  src: url("../fonts/寒蝉活黑体.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳华新体";
  src: url("../fonts/仓耳华新体.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "仓耳今楷03-W03";
  src: url("../fonts/仓耳今楷03-W03.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "方正小标宋_GBK";
  src: url("../fonts/方正小标宋_GBK.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "FZKaiS-Extended";
  src: url("../fonts/方正楷体.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "澳声通拼音鼎楷-简";
  src: url("../fonts/澳声通拼音鼎楷.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "屏显臻宋";
  src: url("../fonts/屏显臻宋.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "ChillHuoSong_F";
  src: url("../fonts/寒蝉活宋体.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

html {
  font-size: 18px;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar {
  display: none;
}

body {
  flex-direction: column;
  position: relative;
  overflow: auto;
}

a {
  outline: none;
  text-decoration: none;
}

.markdown-content {
  padding-bottom: 100px;
}

/* 页眉样式 */
.page-header {
  font-size: 1.3rem;
  font-weight: bolder;
  color: red;
  text-align: center;
  padding: 5px;
  border-bottom: 1px solid #006400;
}

.header-title {
  font-size: 1.6rem;
  font-weight: bold;
  color: #006400;
  text-align: center;
  margin: 0;
}

.bob-page {
  /* border: none; */
  /* border-radius: 20px; */
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1); */
  /* padding: 2.5rem;
  margin: 2rem auto;
  max-width: 1200px; */
  /* font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif; */
  /* font-size: 1.15rem; */
  /* line-height: 1.8; */
  /* color: #333; */
  /* letter-spacing: 0.3px; */
  /* text-rendering: optimizeLegibility; */
  /* transition: all 0.3s ease; */
  /* overflow: hidden; */
  /* position: relative; */
  border-top: 4px solid #4c6ef5;
  /* max-width: 95%; */
  display: grid;
  /* grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); */
  gap: 2rem;
  align-items: start;
  justify-content: center;
  /* padding: 2rem 1.5rem; */
}

/* 链接样式美化 */
.bob-page a {
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 0 2px;
  border-radius: 3px;
}

.bob-page a:hover {
  color: #3b5bdb;
  background-color: rgba(76, 110, 245, 0.1);
}

/* 响应式横向布局调整 */
@media (min-width: 1200px) {
  .bob-page {
    max-width: 1600px;
    /* grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); */
  }
}

@media (max-width: 768px) {
  .bob-page {
    /* grid-template-columns: 1fr; */
    padding: 1.5rem;
  }
}

/* 面包屑导航样式 */
.breadcrumb-nav {
  padding: 8px;
  margin: 15px 0 10px 0;
  background-color: #ddeadc;
  border-radius: 8px;
  font-size: 0.9rem;
  display: none;
  /* 默认隐藏，只在需要时显示 */
}

.breadcrumb-nav.show {
  display: block;
}

.breadcrumb-nav a {
  color: #006400;
  text-decoration: none;
}

.breadcrumb-nav a:hover {
  text-decoration: underline;
}

.breadcrumb-nav .current {
  color: #333;
  font-weight: normal;
}

.breadcrumb-nav .separator {
  color: #999;
  margin: 0 4px;
}

/* 移动端面包屑优化 */
@media (max-width: 768px) {
  .breadcrumb-nav {
    padding: 6px 10px;
    font-size: 0.8rem;
    margin: 12px 0 8px 0;
    border-radius: 6px;
    /* 支持水平滚动 */
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .breadcrumb-nav::-webkit-scrollbar {
    display: none;
  }

  .breadcrumb-nav a {
    font-size: 0.8rem;
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
  }

  .breadcrumb-nav .current {
    font-size: 0.8rem;
    display: inline-block;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: top;
  }

  .breadcrumb-nav .separator {
    margin: 0 2px;
    white-space: nowrap;
    display: inline-block;
  }
}

p {
  margin: 10px 0;
  font-size: 1.2rem;
  text-align: justify;
  border-radius: 5px;
  padding-bottom: 10px;
}

p > strong {
  color: #006400;
}

.header,
.footer {
  font-size: 1.2rem;
  text-align: center;
  padding-bottom: 10px;
}

.header a,
.footer a {
  margin-right: 20px;
  outline: none;
  text-decoration: none;
}

/* 添加导航栏样式 */
.navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  padding: 5px 0;
}

.navigation a {
  font-size: 0.8rem;
  margin: 0 10px;
  padding: 5px 10px;
  text-decoration: none;
  color: #006400;
  border: 1px solid #006400;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.7);
}

.navigation a:hover {
  background-color: #006400;
  color: white;
}

div.title {
  text-align: center;
  margin: 10px 0;
  padding: 5px;
  font-size: 1.4rem;
  font-weight: bolder;
  color: #006400;
  border-radius: 5px;
}

/* 返回顶部按钮样式 */
.totop {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 20px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  padding: 5px 5px;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
}

.totop:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}

/* 搜索触发按钮样式 */
.search-trigger {
  font-size: 18px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 200px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  padding: 5px 5px;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 997;
}

.search-trigger:hover {
  background-color: rgba(0, 100, 0, 0.5);
}

/* 索引管理按钮样式 */
.index-manage {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 80px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 996;
  transition: all 0.3s ease;
}

.index-manage:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}

/* 悬浮搜索框容器 */
.floating-search-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  /* 默认不接收事件 */
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.floating-search-container.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  /* 显示时接收事件 */
}

/* 悬浮搜索框 */
.floating-search-box {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: row; /* 改回水平布局 */
  align-items: center;
  gap: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  touch-action: auto;
}

/* 搜索结果选项卡样式 */
.results-tabs {
  display: flex;
  margin-bottom: 0;
  border-bottom: 2px solid #f0f0f0;
  border-radius: 6px 6px 0 0;
  overflow: hidden;
}

.results-tabs .tab-item {
  flex: 1;
  padding: 5px 8px;
  text-align: center;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
  user-select: none;
  background: #006400;
  border-right: 1px solid #e0e0e0;
}

.results-tabs .tab-item:last-child {
  border-right: none;
}

.results-tabs .tab-item:hover {
  background-color: #005000;
  color: white;
}

.results-tabs .tab-item.active {
  color: #006400;
  border-bottom-color: #006400;
  background-color: white;
  font-weight: bold;
  box-shadow: 0 -2px 4px rgba(0, 100, 0, 0.1);
}

/* 搜索结果头部容器 - 使用flex纵向布局 */
.results-header {
  display: flex;
  flex-direction: column;
  background-color: #006400;
  color: white;
  border-radius: 8px 8px 0 0;
}

/* 结果信息行样式 - 计数和控制按钮在同一行 */
.results-info-line {
  display: flex;
  padding: 5px 10px;
  margin-top: 5px;
}

.results-info-line .results-count {
  font-size: 18px;
  margin: 0;
}

.results-info-line .results-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 0;
}

/* 搜索输入框包装器 */
.search-input-wrapper {
  position: relative;
  flex: 1; /* 在水平布局中占据剩余空间 */
  display: block;
}

.floating-search-box input {
  width: 100%;
  padding: 10px 35px 10px 10px; /* 右侧留出空间给清除按钮 */
  border: 1px solid #006400;
  border-radius: 5px;
  font-size: 16px;
  /* font-family: "LXGW WenKai", sans-serif; */
  outline: none;
  box-sizing: border-box; /* 确保padding不会影响总宽度 */
}

/* 清除搜索按钮 - 确保在搜索框内部的右侧 */
.clear-search-button {
  position: absolute;
  right: 8px; /* 距离搜索框内部右边缘8px */
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  font-size: 14px;
  color: #999;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
  z-index: 10; /* 确保按钮在输入框之上 */
  pointer-events: auto; /* 确保按钮可以接收点击事件 */
}

.clear-search-button:hover {
  background-color: #f0f0f0;
  color: #666;
}

.clear-search-button.visible {
  opacity: 1;
  visibility: visible;
}

.floating-search-buttons {
  display: flex;
  gap: 10px;
}

.floating-search-buttons button {
  padding: 10px 15px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  /* font-family: "LXGW WenKai", sans-serif; */
  white-space: nowrap;
}

#search-button {
  background-color: #006400;
  color: white;
}

#search-button:hover {
  background-color: #004d00;
}

#close-search-button {
  background-color: #f0f0f0;
  color: #333;
}

#close-search-button:hover {
  background-color: #e0e0e0;
}

/* 搜索分类选择器样式 */
.search-category-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
  font-size: 14px;
}

.search-category-selector label {
  color: #333;
  font-weight: 500;
  white-space: nowrap;
}

.search-category-selector select {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.search-category-selector select:focus {
  outline: none;
  border-color: #006400;
  box-shadow: 0 0 0 2px rgba(0, 100, 0, 0.1);
}

.search-category-selector select option {
  padding: 5px;
}

ul {
  font-size: 1.4rem;
  font-weight: bolder;
  padding: 10px 10px;
  margin: 5px 0;
}

ul li {
  list-style: none;
}

nav ul {
  list-style-type: none;
  line-height: 1.5;
  justify-content: space-around;
}

li a {
  text-decoration: none;
}

nav ul li a {
  text-decoration: none;
  color: #006400;
  outline: none;
}

ul.toc-list {
  font-size: 1.2rem;
  color: #006400;
  text-align: left;
  padding: 0;
  margin: 0 10px;
  font-weight: normal;
  text-indent: 0em;
}

div.toc {
  font-size: 1.3rem;
  text-align: center;
  color: red;
}

nav {
  font-size: 1.3rem;
  border-top: 0;
}

.toc {
  border: 1px solid #006400;
}

.toc-item a {
  text-decoration: none;
  color: #006400;
}

.toc-item a:hover {
  color: red;
}

.toc-item-active {
  color: red;
}

.toc-title {
  font-size: 1.3rem;
  font-weight: bolder;
  color: red;
  text-align: center;
  padding: 5px;
  border-bottom: 1px solid #006400;
}

.booktoc-list {
  list-style-type: none;
  padding-left: 20px;
  margin: 0;
}

img {
  width: auto;
  display: block;
  margin: 5px auto;
  border-radius: 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  margin: 10px 0;
  text-decoration: none;
  color: red;
  font-weight: bolder;
}

h1 a:hover,
h2 a:hover,
h3 a:hover,
h4 a:hover,
h5 a:hover,
h6 a:hover {
  color: #006400;
}

h1 a:active,
h2 a:active,
h3 a:active,
h4 a:active,
h5 a:active,
h6 a:active {
  color: red;
}

h1 {
  font-size: 1.4rem;
  text-align: center;
  color: #006400;
  font-weight: bold;
  margin: 5px 0;
  font-family: "方正小标宋_GBK", sans-serif;
}

h2,
h3,
h4,
h5,
h6 {
  font-size: 1.3rem;
  margin: 5px 0;
  padding: 10px 0;
  text-decoration: none;
  font-weight: bold;
  font-family: "方正小标宋_GBK", sans-serif;
}

mark {
  background-color: rgba(158, 222, 158, 0.3);
}

/* 添加搜索框样式 */
.search-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
  padding: 5px;
}

/* #search-input 样式已移动到 .floating-search-box input 中 */

#search-button {
  padding: 8px 15px;
  background-color: #006400;
  color: white;
  border: 1px solid #006400;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  /* font-family: "LXGW WenKai", sans-serif; */
}

#search-button:hover {
  background-color: #004d00;
}

.search-results {
  margin: 0 auto;
  max-width: 90%;
  min-height: 500px;
  /* 增加最小高度 */
  max-height: 98vh;
  /* 增加最大高度 */
  border: 1px solid #006400;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 防止整个容器滚动 */
}

/* 搜索中状态的特殊样式 */
.search-results.loading-state {
  height: 150px;
  /* 搜索中时使用固定的小高度 */
  min-height: 150px;
  max-height: 150px;
}

/* 固定的头部区域 */
.search-results-header {
  flex-shrink: 0;
  /* 不缩小 */
  /* padding: 10px 15px; */
  border-bottom: 1px solid #e0e0e0;
  background-color: #006400;
  color: white;
  border-radius: 5px 5px 0 0;
}

/* 可滚动的内容区域 */
.results-content {
  flex: 1 1 0;
  /* 占据剩余空间，可以缩小 */
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 15px;
  -webkit-overflow-scrolling: touch;
}

/* 固定的底部分页区域 */
.pagination-container {
  flex-shrink: 0;
  /* 不缩小 */
  border-top: 1px solid #e0e0e0;
  background-color: white;
  border-radius: 0 0 5px 5px;
}

.search-results.floating {
  width: 100%;
  height: 100%;
  margin: 0;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.25);
  padding: 0;
  /* 保持flex布局，不要覆盖为block */
}

.results-count {
  font-weight: bold;
  font-size: 1.1rem;
  margin: 0;
  flex: 1;
}

.powered-by {
  font-size: 0.8rem;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8px;
}

.results-controls {
  display: flex;
  gap: 5px;
  align-items: center;
  margin: 0;
}

.expand-all-btn,
.collapse-all-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.9rem;
  cursor: pointer;
  white-space: nowrap;
}

.expand-all-btn:hover,
.collapse-all-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.close-results-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0 5px;
  margin: 0;
  line-height: 1;
}

.close-results-btn:hover {
  color: #f0f0f0;
}

.search-result-item {
  margin: 0;
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: rgba(0, 100, 0, 0.02);
}

.search-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-title {
  font-weight: bold;
  color: #006400;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-result-title:hover {
  background-color: rgba(0, 100, 0, 0.1);
}

.toggle-icon {
  margin-left: auto;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: rotate(0deg);
}

.toggle-icon.collapsed {
  transform: rotate(-90deg);
}

.search-result-contexts {
  margin-left: 10px;
  border-left: 3px solid #eee;
  padding-left: 10px;
  padding-top: 5px;
  padding-bottom: 5px;
  transition: all 0.3s ease;
}

/* 匹配项样式 */
.context-item {
  margin: 5px 0;
  padding: 5px;
  border-bottom: 1px dashed #eee;
  display: flex;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.context-item:hover {
  background-color: rgba(0, 100, 0, 0.1);
}

.context-item:last-child {
  border-bottom: none;
}

.context-number {
  flex: 0 0 25px;
  font-weight: bold;
  color: #006400;
}

.context-text {
  flex: 1;
}

.title-match {
  font-weight: bold;
  color: #006400;
  background-color: rgba(0, 100, 0, 0.05);
  border-radius: 3px;
}

.content-match {
  color: #333;
}

.footnote-match {
  color: #ff6600;
  background-color: rgba(255, 102, 0, 0.05);
  border-radius: 3px;
  border-left: 3px solid #ff6600;
  padding-left: 8px;
}

.footnote-match .context-number {
  color: #ff6600;
}

.footnote-match::before {
  content: "📝 ";
  font-size: 0.8rem;
  margin-right: 4px;
}

.more-matches {
  font-style: italic;
  color: #666;
  text-align: right;
  margin-top: 5px;
  font-size: 0.9rem;
}

.match-count {
  font-size: 0.9rem;
  color: #666;
  font-weight: normal;
  margin-left: 5px;
  margin-right: 5px;
}

/* 相关性评分样式 */
.relevance-score {
  font-size: 0.8rem;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  margin-right: 5px;
}

.relevance-score.high-relevance {
  background-color: #4caf50;
  color: white;
}

.relevance-score.medium-relevance {
  background-color: #ff9800;
  color: white;
}

.relevance-score.low-relevance {
  background-color: #9e9e9e;
  color: white;
}

/* 章节内容中的搜索信息样式 */
.search-info {
  margin: 10px 0;
  padding: 10px;
  background-color: rgba(0, 100, 0, 0.1);
  border: 1px solid #006400;
  border-radius: 5px;
  font-size: 1rem;
}

/* 悬浮搜索导航面板样式 */
.search-info.floating {
  position: fixed;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  margin: 0;
  transition: all 0.3s ease;
}

/* 在移动设备上调整悬浮面板位置 */
@media (max-width: 768px) {
  .search-info.floating {
    bottom: 5px;
    width: 300px;
  }
}

.search-info-text {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.highlight-count {
  font-weight: bold;
  color: #006400;
  margin: 0 5px;
}

.highlight-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: flex-end;
}

.nav-highlight-btn,
.clear-highlight-btn {
  padding: 3px 8px;
  background-color: #006400;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  /* font-size: 0.9rem; */
}

.nav-highlight-btn:hover,
.clear-highlight-btn:hover {
  background-color: #004d00;
}

.current-highlight,
.total-highlights {
  font-weight: bold;
  color: #006400;
}

/* 使章节内容中的高亮更明显 */
#content .search-highlight {
  background-color: #ffff00;
  color: #000;
  font-weight: bold;
  padding: 0 2px;
  border-radius: 3px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

/* 当前激活的高亮样式 */
#content .search-highlight.active-highlight {
  background-color: #ff9900;
  box-shadow: 0 0 5px rgba(255, 153, 0, 0.5);
  position: relative;
  z-index: 2;
}

/* 搜索结果区域样式增强 */
.floating-results-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  /* 默认不接收事件 */
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.floating-results-container.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  /* 显示时接收事件 */
}

/* 搜索高亮样式 */
.search-highlight {
  background-color: #ffff00;
  color: #000;
  font-weight: bold;
  padding: 0 1px;
  border-radius: 2px;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
}

html.no-scroll body {
  padding-right: 15px;
  /* 模拟滚动条宽度，防止布局偏移 */
}

/* 弹窗显示时禁止底部内容滚动 */
html.modal-open,
html.modal-open body {
  overflow: hidden;
  touch-action: none;
}

/* 但是允许搜索结果内容区域滚动 */
html.modal-open .results-content,
html.modal-open .search-results .results-content,
.search-results .results-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 允许脚注弹窗内容区域滚动 */
html.modal-open .footnote-popup-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 确保脚注弹窗整体也允许滚动事件 */
html.modal-open .footnote-popup {
  touch-action: auto !important;
}

/* 允许设置面板内容滚动 */
html.modal-open .settings-content {
  overflow-y: auto !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 允许索引管理面板内容滚动 */
html.modal-open #index-management {
  overflow-y: auto !important;
  touch-action: auto !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 确保弹窗内容可滚动 */
.floating-search-box {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  touch-action: auto;
}

/* 弹窗显示时的覆盖层样式 */
.floating-search-container.active:before,
.floating-results-container.active:before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* 脚注引用样式 */
.footnote-ref {
  text-decoration: none;
  color: #006400;
  font-weight: bold;
  font-size: 0.8em;
  vertical-align: super;
}

.footnote-ref:hover {
  text-decoration: underline;
  color: #004d00;
}

/* 脚注弹窗 Dialog 样式 */
.footnote-dialog {
  /* 居中定位：使用固定尺寸和居中对齐 */
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  /* 弹窗尺寸：响应式设计 */
  width: min(90vw, 1200px);
  max-height: 80vh;

  border: 2px solid #006400;
  border-radius: 8px;
  padding: 0;
  margin: 0;

  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  font-family: var(--content-font-family, inherit);
  font-size: var(--content-font-size, 16px);
  overflow: hidden;

  /* Mac/iOS 优化 */
  box-sizing: border-box;
  -webkit-transform: translate(-50%, -50%) translateZ(0);
  will-change: transform;

  /* 默认隐藏弹窗 */
  display: none;
}

/* 只有当 dialog 打开时才显示为 flex */
.footnote-dialog[open] {
  display: flex !important;
  flex-direction: column;
  /* 确保居中变换正确应用 */
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) translateZ(0) !important;
}

.footnote-dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
}

/* 当 dialog 打开时禁用背景滚动 */
body:has(.footnote-dialog[open]) {
  overflow: hidden;
}

/* 兼容不支持 :has() 的浏览器 */
body.dialog-open {
  overflow: hidden;
}

.footnote-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px 6px 0 0;
  flex-shrink: 0;
}

.footnote-dialog-header h4 {
  margin: 0;
  font-weight: bold;
  color: #006400;
  font-size: 0.9em;
  font-family: inherit;
}

.footnote-close-btn {
  background: none;
  border: none;
  font-size: 1.2em;
  color: #666;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  font-family: inherit;
}

.footnote-close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.footnote-dialog-content {
  /* Mac/iOS 兼容性修复：确保内容容器占满剩余空间 */
  flex: 1 1 auto;
  padding: 15px;
  line-height: var(--content-line-height, 1.5);
  color: #333;
  overflow-y: auto;

  /* 关键修复：确保最小高度和正确的盒模型 */
  min-height: 0;
  box-sizing: border-box;

  /* Mac/iOS 滚动优化 */
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  font-size: inherit;
  font-family: inherit;
}

.footnote-dialog-content p {
  margin: 0 0 10px 0;
  line-height: inherit;
  font-size: inherit;
  font-family: inherit;
}

.footnote-dialog-content p:last-child {
  margin-bottom: 0;
}

.footnote-dialog-content strong {
  font-weight: bold;
}

.footnote-dialog-content em {
  font-style: italic;
}

.footnote-dialog-content a {
  color: #006400;
  text-decoration: none;
}

.footnote-dialog-content a:hover {
  text-decoration: underline;
}

/* 脚注弹窗背景遮罩 */
.footnote-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.footnote-popup-overlay.show {
  opacity: 1;
}

/* 脚注弹窗样式 */
.footnote-popup {
  position: fixed;
  left: 50%;
  top: 50%;
  background: white;
  border: 2px solid #006400;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
  /* max-width: 500px; */
  /* min-width: 350px; */
  width: 90%;
  height: auto;
  z-index: 10000;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

/* .footnote-popup {
    max-width: 95vw;
    min-width: 300px;
    font-size: 0.85rem;
    max-height: 80vh;
  } */

.footnote-popup.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.footnote-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 6px 6px 0 0;
}

.footnote-popup-title {
  font-weight: bold;
  color: #006400;
  font-size: 0.9rem;
}

.footnote-popup-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.footnote-popup-close:hover {
  background: #e9ecef;
  color: #333;
}

.footnote-popup-content {
  padding: 15px;
  line-height: 1.5;
  color: #333;
  max-height: 500px;
  overflow-y: auto;
}

.footnote-popup-content p {
  margin: 0 0 10px 0;
  line-height: 1.6;
}

.footnote-popup-content p:last-child {
  margin-bottom: 0;
}

.footnote-popup-content strong {
  font-weight: bold;
}

.footnote-popup-content em {
  font-style: italic;
}

.footnote-popup-content a {
  color: #006400;
  text-decoration: none;
}

.footnote-popup-content a:hover {
  text-decoration: underline;
}

.footnote-popup-footer {
  padding: 10px 15px;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 0 0 6px 6px;
  text-align: center;
}

.footnote-popup-close-btn {
  background: #006400;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s ease;
}

.footnote-popup-close-btn:hover {
  background: #004d00;
}

/* 移动端脚注优化 */
@media (max-width: 768px) {
  .footnotes {
    font-size: 0.8em;
    margin-top: 1.5em;
  }

  .footnote-ref {
    font-size: 0.75em;
  }

  .footnotes-list {
    padding-left: 1.2em;
  }

  .footnote-popup-content {
    max-height: 500px;
  }

  .footnote-popup-header,
  .footnote-popup-footer {
    padding: 8px 12px;
  }

  /* Dialog 移动端样式 */
  .footnote-dialog {
    /* 移动端居中定位 */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    /* 移动端尺寸优化 */
    width: 95vw;
    max-height: 85vh;
    margin: 0;

    /* 不要在这里设置 display，让默认的隐藏状态生效 */
  }

  .footnote-dialog[open] {
    /* 确保移动端也正确居中 */
    transform: translate(-50%, -50%) translateZ(0);
  }

  .footnote-dialog[open] {
    display: flex !important;
    flex-direction: column;
  }

  .footnote-dialog-header {
    padding: 8px 12px;
  }

  .footnote-dialog-header h4 {
    font-size: 0.85em;
  }

  .footnote-dialog-content {
    /* Mac/iOS 移动端内容容器修复 */
    flex: 1 1 auto !important;
    padding: 12px;
    overflow-y: auto;
    overflow-x: hidden;

    /* 关键修复：确保内容容器高度正确 */
    min-height: 0 !important;
    box-sizing: border-box !important;

    /* 移除 max-height 限制，让 flex 自动计算 */
    /* max-height: calc(100vh - 120px); */

    /* iOS 滚动优化 */
    -webkit-overflow-scrolling: touch !important;
    overscroll-behavior: contain;
    touch-action: pan-y;

    /* 硬件加速 */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: scroll-position;
  }
}

/* 专门针对触摸设备的额外样式 */
@media (hover: none) and (pointer: coarse) {
  .footnote-dialog-content {
    flex: 1 1 auto !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
    min-height: 0 !important;
    /* 确保在触摸设备上能够滚动 */
    -webkit-transform: translate3d(0, 0, 0) !important;
    transform: translate3d(0, 0, 0) !important;
  }
}

/* ===== 圣经经文引用样式 ===== */
.bible-reference {
  color: #0066cc;
  text-decoration: underline;
  cursor: pointer;
  font-weight: bold;
  border-radius: 2px;
  padding: 1px 2px;
  transition: all 0.2s ease;
}

.bible-reference:hover {
  color: #004499;
  background-color: #e6f3ff;
  text-decoration: none;
}

.bible-reference:active {
  background-color: #cce7ff;
}

/* Safari/WebKit 脚注弹窗特定修复 */
@supports (-webkit-appearance: none) {
  .footnote-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) translateZ(0) !important;

    width: 90vw !important;
    max-height: 80vh !important;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
  }

  .footnote-dialog[open] {
    display: flex !important;
    flex-direction: column !important;
    transform: translate(-50%, -50%) translateZ(0) !important;
  }

  .footnote-dialog-content {
    flex: 1 1 auto !important;
    min-height: 0 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    box-sizing: border-box !important;
  }

  /* 移动端Safari特殊处理 */
  @media (max-width: 768px) {
    .footnote-dialog {
      width: 95vw !important;
      max-height: 85vh !important;
    }
  }
}

/* 加载状态样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.1rem;
  color: #666;
  background-color: rgba(255, 255, 255, 0.9);
}

/* 加载状态样式 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.1rem;
  color: #666;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
}

/* 搜索错误状态样式 */
.search-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.1rem;
  color: #dc3545;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  text-align: center;
}

/* 无搜索结果状态样式 */
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 1.1rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  text-align: center;
  margin: 20px;
}

/* 分页样式 */
.pagination-container {
  margin: 15px 0;
  padding: 0 20px;
}

.pagination {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.pagination-info {
  font-size: 0.9rem;
  color: #666;
  text-align: center;
}

.pagination-buttons {
  display: flex;
  gap: 3px;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: center;
  overflow-x: auto;
}

.pagination-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  min-width: 32px;
  text-align: center;
  white-space: nowrap;
}

.pagination-btn:hover {
  background: #f5f5f5;
  border-color: #006400;
}

.pagination-btn.active {
  background: #006400;
  color: white;
  border-color: #006400;
}

.pagination-btn:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
  border-color: #eee;
}

.pagination-ellipsis {
  padding: 8px 4px;
  color: #999;
  font-size: 0.9rem;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination-buttons {
    gap: 2px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .pagination-btn {
    padding: 5px 6px;
    font-size: 0.75rem;
    min-width: 28px;
    flex-shrink: 0;
  }

  .pagination-ellipsis {
    padding: 5px 2px;
    font-size: 0.75rem;
  }
}

/* ===== 设置面板样式 ===== */
:root {
  --content-font-size: 16px;
  --content-line-height: 1.6;
  --content-max-width: 800px;
}

/* 应用设置变量到内容区域 */
#content {
  font-size: var(--content-font-size);
  font-family: var(--content-font-family);
  line-height: var(--content-line-height);
  max-width: var(--content-max-width);
  margin: 10px auto;
  flex: 1;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

#content p {
  font-size: var(--content-font-size);
  line-height: var(--content-line-height);
}

/* 背景主题 */
body.bg-default {
  background-color: #ffffff;
  color: #333333;
}

body.bg-warm {
  background-color: #fdf6e3;
}

body.bg-dark {
  background-color: #2b2b2b;
  color: #e0e0e0;
}

body.bg-dark #content {
  color: #e0e0e0;
}

body.bg-dark .page-header {
  color: #4caf50;
  border-bottom-color: #4caf50;
}

body.bg-dark .header-title {
  color: #4caf50;
}

body.bg-green {
  background-color: #f0f8f0;
}

.settings-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.settings-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  padding: 5px;
  max-height: 100%;
}

/* 移动端优化 */
@media (max-width: 768px) {
  :root {
    --content-max-width: 92%;
  }

  #content {
    padding: 15px 10px;
    margin: 10px auto;
  }

  h1 {
    font-size: 1.5rem;
  }

  h2 {
    font-size: 1.3rem;
  }

  h3 {
    font-size: 1.2rem;
  }

  p {
    font-size: 1.1rem;
    margin: 12px 0;
  }
}

.settings-close {
  width: 30px;
  height: 30px;
  font-size: 18px;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.settings-header h3 {
  margin: 0;
  color: #333;
  font-size: 1em;
}

.settings-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.settings-close:hover {
  background-color: #f0f0f0;
}

.settings-section h4 {
  margin: 0 0 15px 0;
  color: #444;
  font-size: 0.9em;
  font-weight: 600;
}

/* 移动端section优化 */
@media (max-width: 768px) {
  .settings-section h4 {
    font-size: 0.9em;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #f0f0f0;
  }

  /* 在移动端隐藏页面设置部分 */
  .settings-section.page-settings {
    display: none;
  }
}

/* 颜色选项 */
.color-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.color-option {
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  font-size: 0.9em;
  transition: all 0.2s;
  position: relative;
}

/* 移动端颜色选项优化 */
@media (max-width: 768px) {
  .color-options {
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
  }

  .color-option {
    padding: 6px 3px;
    font-size: 0.7em;
    min-height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .color-option.selected::after {
    font-size: 12px;
    top: 2px;
    right: 3px;
  }
}

.color-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.color-option.selected {
  border-color: #006400 !important;
  border-width: 3px !important;
}

.color-option.selected::after {
  content: "✓";
  position: absolute;
  top: 5px;
  right: 8px;
  color: #006400;
  font-weight: bold;
  font-size: 16px;
}

/* 字体设置 */
.font-options,
.font-family-options,
.line-height-options,
.width-options {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.font-options label,
.font-family-options label,
.line-height-options label,
.width-options label {
  min-width: 80px;
  font-size: 0.9em;
  color: #555;
}

.font-options input[type="range"],
.line-height-options input[type="range"],
.width-options input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.font-family-options select {
  flex: 1;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9em;
}

/* 移动端字体设置优化 */
@media (max-width: 768px) {
  .font-options,
  .font-family-options,
  .line-height-options,
  .width-options {
    flex-direction: column;
    gap: 5px;
    margin-bottom: 12px;
  }

  .font-options label,
  .font-family-options label,
  .line-height-options label,
  .width-options label {
    min-width: auto;
    font-size: 0.8em;
    font-weight: 600;
    margin-bottom: 3px;
  }

  .font-options input[type="range"],
  .line-height-options input[type="range"],
  .width-options input[type="range"] {
    margin: 3px 0;
    width: 100%;
    height: 30px;
  }

  .font-family-options select {
    padding: 8px;
    font-size: 0.8em;
    border-radius: 4px;
  }

  #font-size-value,
  #line-height-value,
  #content-width-value {
    font-size: 0.8em;
    text-align: center;
    background: #f5f5f5;
    padding: 3px 8px;
    border-radius: 3px;
    margin-top: 3px;
  }
}

#font-size-value,
#line-height-value,
#content-width-value {
  min-width: 50px;
  font-size: 0.9em;
  color: #666;
  font-weight: 500;
}

/* 设置按钮 */
.settings-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.reset-btn,
.close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
  .settings-actions {
    flex-direction: row;
    gap: 8px;
    margin-top: 15px;
    padding-top: 10px;
  }

  .reset-btn,
  .close-btn {
    padding: 10px 16px;
    font-size: 0.85em;
    border-radius: 6px;
    flex: 1;
    font-weight: 600;
  }
}

.reset-btn {
  background: #f44336;
  color: white;
}

.reset-btn:hover {
  background: #d32f2f;
}

.close-btn {
  background: #006400;
  color: white;
}

.close-btn:hover {
  background: #004d00;
}

/* 阅读设置按钮样式 */
.reading-settings {
  font-size: 16px;
  text-align: center;
  position: fixed;
  width: 40px;
  height: 40px;
  bottom: 140px;
  right: 5px;
  background-color: rgba(66, 100, 66, 0.5);
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(45, 58, 49, 0.3);
  -webkit-tap-highlight-color: transparent;
  z-index: 995;
  transition: all 0.3s ease;
}

.reading-settings:hover {
  background-color: rgba(0, 100, 0, 0.5);
  transform: scale(1.1);
}

/* ===== 全屏章节弹窗样式 ===== */
.chapter-modal-dialog {
  /* 使用 inset 替代 top/left/width/height，Mac/iOS 兼容性更好 */
  position: fixed;
  inset: 0;
  z-index: 10001;

  /* 移除 100vh/100vw，避免 Safari viewport 计算问题 */
  margin: 0;
  padding: 0;
  border: none;
  border-radius: 0;
  outline: none;

  background: var(--bg-color, #ffffff);
  overflow: hidden;
  box-sizing: border-box;

  /* Safari/WebKit 优化 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.chapter-modal-dialog::backdrop {
  background: rgba(0, 0, 0, 0.8);
}

.chapter-modal-content {
  /* 使用 inset 确保完整覆盖，避免高度计算问题 */
  position: absolute;
  inset: 0;

  display: flex;
  flex-direction: column;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #333333);
  box-sizing: border-box;

  /* Mac/iOS 优化 */
  min-height: 100%;
  -webkit-overflow-scrolling: touch;
}

.chapter-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  background: var(--header-bg, #f8f9fa);
  flex-shrink: 0;
}

.chapter-modal-title {
  margin: 0;
  font-size: 1em;
  font-weight: 500;
  color: var(--text-color, #333333);
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chapter-modal-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.modal-close-btn {
  background: var(--button-bg, #ffffff);
  border: 1px solid var(--border-color, #ddd);
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-color, #333);
  transition: all 0.2s ease;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: var(--button-hover-bg, #f0f0f0);
  border-color: var(--button-hover-border, #ccc);
}

.modal-close-btn {
  background: #ff4757;
  color: white;
  border-color: #ff4757;
}

.modal-close-btn:hover {
  background: #ff3742;
  border-color: #ff3742;
}

.chapter-modal-body {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.modal-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--text-color, #666);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color, #e0e0e0);
  border-top: 4px solid var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.modal-chapter-content {
  /* 使用 flex: 1 确保占满剩余空间，避免高度计算问题 */
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 24px;
  line-height: var(--content-line-height, 1.6);
  font-size: var(--content-font-size, 16px);
  font-family: var(--content-font-family, '"霞鹜文楷", sans-serif');

  /* Mac/iOS 兼容性修复 */
  position: relative;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  /* 确保最小高度，避免内容过少时高度塌陷 */
  min-height: 0;
}

.modal-chapter-content p {
  margin-bottom: 16px;
}

.modal-chapter-content .search-highlight {
  background-color: yellow;
  font-weight: bold;
  padding: 2px 4px;
  border-radius: 3px;
}

.error-message {
  text-align: center;
  padding: 40px;
  color: var(--error-color, #dc3545);
}

.error-message h3 {
  margin-bottom: 16px;
  color: var(--error-color, #dc3545);
}

.retry-btn {
  background: var(--primary-color, #007bff);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 16px;
}

.retry-btn:hover {
  background: var(--primary-hover-color, #0056b3);
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .chapter-modal-header {
    padding: 12px 16px;
  }

  .chapter-modal-title {
    font-size: 0.95em;
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
    line-height: 1.3;
    padding-right: 8px;
  }

  .modal-close-btn {
    padding: 4px 6px;
    font-size: 11px;
    min-width: 24px;
    height: 24px;
    flex-shrink: 0;
  }

  .modal-chapter-content {
    padding: 16px;
    /* 移动设备上也使用用户设置的字体大小，但稍微调小一点 */
    font-size: calc(var(--content-font-size, 16px) * 0.9);
  }
}

/* Safari/WebKit 特定修复 */
@supports (-webkit-appearance: none) {
  .chapter-modal-dialog {
    /* Safari 中使用 inset 而不是 100vh/100vw */
    position: fixed !important;
    inset: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    outline: none !important;
    /* 移除 width/height 设置，使用 inset */
  }

  .chapter-modal-content {
    /* Safari 中确保内容区域正确显示 */
    position: absolute !important;
    inset: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: 100% !important;
  }

  .modal-chapter-content {
    /* Safari 中的滚动和布局修复 */
    flex: 1 1 auto !important;
    -webkit-overflow-scrolling: touch !important;
    overflow-y: auto !important;
    min-height: 0 !important;
  }
}
