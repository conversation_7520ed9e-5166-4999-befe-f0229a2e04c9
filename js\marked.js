/**
 * marked v16.1.2 - a markdown parser
 * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)
 * https://github.com/markedjs/marked
 */

/**
 * DO NOT EDIT THIS FILE
 * The code in this file is generated from files in ./src/
 */
(function (g, f) {
  if (typeof exports == "object" && typeof module < "u") {
    module.exports = f();
  } else if ("function" == typeof define && define.amd) {
    define("marked", f);
  } else {
    g["marked"] = f();
  }
})(
  typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : this,
  function () {
    var exports = {};
    var __exports = exports;
    var module = { exports };
    ("use strict");
    var H = Object.defineProperty;
    var be = Object.getOwnPropertyDescriptor;
    var Re = Object.getOwnPropertyNames;
    var Oe = Object.prototype.hasOwnProperty;
    var Te = (a, e) => {
        for (var t in e) H(a, t, { get: e[t], enumerable: !0 });
      },
      we = (a, e, t, n) => {
        if ((e && typeof e == "object") || typeof e == "function")
          for (let r of Re(e))
            !Oe.call(a, r) &&
              r !== t &&
              H(a, r, {
                get: () => e[r],
                enumerable: !(n = be(e, r)) || n.enumerable,
              });
        return a;
      };
    var ye = (a) => we(H({}, "__esModule", { value: !0 }), a);
    var dt = {};
    Te(dt, {
      Hooks: () => $,
      Lexer: () => x,
      Marked: () => A,
      Parser: () => b,
      Renderer: () => P,
      TextRenderer: () => S,
      Tokenizer: () => y,
      defaults: () => O,
      getDefaults: () => _,
      lexer: () => ht,
      marked: () => d,
      options: () => it,
      parse: () => pt,
      parseInline: () => ut,
      parser: () => ct,
      setOptions: () => ot,
      use: () => at,
      walkTokens: () => lt,
    });
    module.exports = ye(dt);
    function _() {
      return {
        async: !1,
        breaks: !1,
        extensions: null,
        gfm: !0,
        hooks: null,
        pedantic: !1,
        renderer: null,
        silent: !1,
        tokenizer: null,
        walkTokens: null,
      };
    }
    var O = _();
    function N(a) {
      O = a;
    }
    var C = { exec: () => null };
    function h(a, e = "") {
      let t = typeof a == "string" ? a : a.source,
        n = {
          replace: (r, i) => {
            let s = typeof i == "string" ? i : i.source;
            return (s = s.replace(m.caret, "$1")), (t = t.replace(r, s)), n;
          },
          getRegex: () => new RegExp(t, e),
        };
      return n;
    }
    var m = {
        codeRemoveIndent: /^(?: {1,4}| {0,3}\t)/gm,
        outputLinkReplace: /\\([\[\]])/g,
        indentCodeCompensation: /^(\s+)(?:```)/,
        beginningSpace: /^\s+/,
        endingHash: /#$/,
        startingSpaceChar: /^ /,
        endingSpaceChar: / $/,
        nonSpaceChar: /[^ ]/,
        newLineCharGlobal: /\n/g,
        tabCharGlobal: /\t/g,
        multipleSpaceGlobal: /\s+/g,
        blankLine: /^[ \t]*$/,
        doubleBlankLine: /\n[ \t]*\n[ \t]*$/,
        blockquoteStart: /^ {0,3}>/,
        blockquoteSetextReplace: /\n {0,3}((?:=+|-+) *)(?=\n|$)/g,
        blockquoteSetextReplace2: /^ {0,3}>[ \t]?/gm,
        listReplaceTabs: /^\t+/,
        listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,
        listIsTask: /^\[[ xX]\] /,
        listReplaceTask: /^\[[ xX]\] +/,
        anyLine: /\n.*\n/,
        hrefBrackets: /^<(.*)>$/,
        tableDelimiter: /[:|]/,
        tableAlignChars: /^\||\| *$/g,
        tableRowBlankLine: /\n[ \t]*$/,
        tableAlignRight: /^ *-+: *$/,
        tableAlignCenter: /^ *:-+: *$/,
        tableAlignLeft: /^ *:-+ *$/,
        startATag: /^<a /i,
        endATag: /^<\/a>/i,
        startPreScriptTag: /^<(pre|code|kbd|script)(\s|>)/i,
        endPreScriptTag: /^<\/(pre|code|kbd|script)(\s|>)/i,
        startAngleBracket: /^</,
        endAngleBracket: />$/,
        pedanticHrefTitle: /^([^'"]*[^\s])\s+(['"])(.*)\2/,
        unicodeAlphaNumeric: /[\p{L}\p{N}]/u,
        escapeTest: /[&<>"']/,
        escapeReplace: /[&<>"']/g,
        escapeTestNoEncode: /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,
        escapeReplaceNoEncode:
          /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,
        unescapeTest: /&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,
        caret: /(^|[^\[])\^/g,
        percentDecode: /%25/g,
        findPipe: /\|/g,
        splitPipe: / \|/,
        slashPipe: /\\\|/g,
        carriageReturn: /\r\n|\r/g,
        spaceLine: /^ +$/gm,
        notSpaceStart: /^\S*/,
        endingNewline: /\n$/,
        listItemRegex: (a) =>
          new RegExp(`^( {0,3}${a})((?:[	 ][^\\n]*)?(?:\\n|$))`),
        nextBulletRegex: (a) =>
          new RegExp(
            `^ {0,${Math.min(
              3,
              a - 1
            )}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`
          ),
        hrRegex: (a) =>
          new RegExp(
            `^ {0,${Math.min(
              3,
              a - 1
            )}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`
          ),
        fencesBeginRegex: (a) =>
          new RegExp(`^ {0,${Math.min(3, a - 1)}}(?:\`\`\`|~~~)`),
        headingBeginRegex: (a) => new RegExp(`^ {0,${Math.min(3, a - 1)}}#`),
        htmlBeginRegex: (a) =>
          new RegExp(`^ {0,${Math.min(3, a - 1)}}<(?:[a-z].*>|!--)`, "i"),
      },
      Pe = /^(?:[ \t]*(?:\n|$))+/,
      Se = /^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,
      $e =
        /^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,
      I = /^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,
      _e = /^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,
      F = /(?:[*+-]|\d{1,9}[.)])/,
      ie =
        /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,
      oe = h(ie)
        .replace(/bull/g, F)
        .replace(/blockCode/g, /(?: {4}| {0,3}\t)/)
        .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/)
        .replace(/blockquote/g, / {0,3}>/)
        .replace(/heading/g, / {0,3}#{1,6}/)
        .replace(/html/g, / {0,3}<[^\n>]+>\n/)
        .replace(/\|table/g, "")
        .getRegex(),
      Le = h(ie)
        .replace(/bull/g, F)
        .replace(/blockCode/g, /(?: {4}| {0,3}\t)/)
        .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/)
        .replace(/blockquote/g, / {0,3}>/)
        .replace(/heading/g, / {0,3}#{1,6}/)
        .replace(/html/g, / {0,3}<[^\n>]+>\n/)
        .replace(/table/g, / {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/)
        .getRegex(),
      Q =
        /^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,
      Me = /^[^\n]+/,
      U = /(?!\s*\])(?:\\.|[^\[\]\\])+/,
      ze = h(
        /^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/
      )
        .replace("label", U)
        .replace(
          "title",
          /(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/
        )
        .getRegex(),
      Ae = h(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/)
        .replace(/bull/g, F)
        .getRegex(),
      v =
        "address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",
      K = /<!--(?:-?>|[\s\S]*?(?:-->|$))/,
      Ee = h(
        "^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))",
        "i"
      )
        .replace("comment", K)
        .replace("tag", v)
        .replace(
          "attribute",
          / +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/
        )
        .getRegex(),
      ae = h(Q)
        .replace("hr", I)
        .replace("heading", " {0,3}#{1,6}(?:\\s|$)")
        .replace("|lheading", "")
        .replace("|table", "")
        .replace("blockquote", " {0,3}>")
        .replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n")
        .replace("list", " {0,3}(?:[*+-]|1[.)]) ")
        .replace(
          "html",
          "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)"
        )
        .replace("tag", v)
        .getRegex(),
      Ce = h(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/)
        .replace("paragraph", ae)
        .getRegex(),
      W = {
        blockquote: Ce,
        code: Se,
        def: ze,
        fences: $e,
        heading: _e,
        hr: I,
        html: Ee,
        lheading: oe,
        list: Ae,
        newline: Pe,
        paragraph: ae,
        table: C,
        text: Me,
      },
      se = h(
        "^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"
      )
        .replace("hr", I)
        .replace("heading", " {0,3}#{1,6}(?:\\s|$)")
        .replace("blockquote", " {0,3}>")
        .replace("code", "(?: {4}| {0,3}	)[^\\n]")
        .replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n")
        .replace("list", " {0,3}(?:[*+-]|1[.)]) ")
        .replace(
          "html",
          "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)"
        )
        .replace("tag", v)
        .getRegex(),
      Ie = {
        ...W,
        lheading: Le,
        table: se,
        paragraph: h(Q)
          .replace("hr", I)
          .replace("heading", " {0,3}#{1,6}(?:\\s|$)")
          .replace("|lheading", "")
          .replace("table", se)
          .replace("blockquote", " {0,3}>")
          .replace("fences", " {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n")
          .replace("list", " {0,3}(?:[*+-]|1[.)]) ")
          .replace(
            "html",
            "</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)"
          )
          .replace("tag", v)
          .getRegex(),
      },
      Be = {
        ...W,
        html: h(
          `^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`
        )
          .replace("comment", K)
          .replace(
            /tag/g,
            "(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b"
          )
          .getRegex(),
        def: /^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,
        heading: /^(#{1,6})(.*)(?:\n+|$)/,
        fences: C,
        lheading: /^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,
        paragraph: h(Q)
          .replace("hr", I)
          .replace(
            "heading",
            ` *#{1,6} *[^
]`
          )
          .replace("lheading", oe)
          .replace("|table", "")
          .replace("blockquote", " {0,3}>")
          .replace("|fences", "")
          .replace("|list", "")
          .replace("|html", "")
          .replace("|tag", "")
          .getRegex(),
      },
      qe = /^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,
      ve = /^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,
      le = /^( {2,}|\\)\n(?!\s*$)/,
      De =
        /^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,
      D = /[\p{P}\p{S}]/u,
      X = /[\s\p{P}\p{S}]/u,
      ue = /[^\s\p{P}\p{S}]/u,
      Ze = h(/^((?![*_])punctSpace)/, "u")
        .replace(/punctSpace/g, X)
        .getRegex(),
      pe = /(?!~)[\p{P}\p{S}]/u,
      Ge = /(?!~)[\s\p{P}\p{S}]/u,
      He = /(?:[^\s\p{P}\p{S}]|~)/u,
      Ne =
        /\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,
      ce = /^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,
      je = h(ce, "u").replace(/punct/g, D).getRegex(),
      Fe = h(ce, "u").replace(/punct/g, pe).getRegex(),
      he =
        "^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",
      Qe = h(he, "gu")
        .replace(/notPunctSpace/g, ue)
        .replace(/punctSpace/g, X)
        .replace(/punct/g, D)
        .getRegex(),
      Ue = h(he, "gu")
        .replace(/notPunctSpace/g, He)
        .replace(/punctSpace/g, Ge)
        .replace(/punct/g, pe)
        .getRegex(),
      Ke = h(
        "^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)",
        "gu"
      )
        .replace(/notPunctSpace/g, ue)
        .replace(/punctSpace/g, X)
        .replace(/punct/g, D)
        .getRegex(),
      We = h(/\\(punct)/, "gu")
        .replace(/punct/g, D)
        .getRegex(),
      Xe = h(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/)
        .replace("scheme", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)
        .replace(
          "email",
          /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/
        )
        .getRegex(),
      Je = h(K).replace("(?:-->|$)", "-->").getRegex(),
      Ve = h(
        "^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>"
      )
        .replace("comment", Je)
        .replace(
          "attribute",
          /\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/
        )
        .getRegex(),
      q = /(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,
      Ye = h(
        /^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/
      )
        .replace("label", q)
        .replace("href", /<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/)
        .replace(
          "title",
          /"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/
        )
        .getRegex(),
      de = h(/^!?\[(label)\]\[(ref)\]/)
        .replace("label", q)
        .replace("ref", U)
        .getRegex(),
      ke = h(/^!?\[(ref)\](?:\[\])?/)
        .replace("ref", U)
        .getRegex(),
      et = h("reflink|nolink(?!\\()", "g")
        .replace("reflink", de)
        .replace("nolink", ke)
        .getRegex(),
      J = {
        _backpedal: C,
        anyPunctuation: We,
        autolink: Xe,
        blockSkip: Ne,
        br: le,
        code: ve,
        del: C,
        emStrongLDelim: je,
        emStrongRDelimAst: Qe,
        emStrongRDelimUnd: Ke,
        escape: qe,
        link: Ye,
        nolink: ke,
        punctuation: Ze,
        reflink: de,
        reflinkSearch: et,
        tag: Ve,
        text: De,
        url: C,
      },
      tt = {
        ...J,
        link: h(/^!?\[(label)\]\((.*?)\)/)
          .replace("label", q)
          .getRegex(),
        reflink: h(/^!?\[(label)\]\s*\[([^\]]*)\]/)
          .replace("label", q)
          .getRegex(),
      },
      j = {
        ...J,
        emStrongRDelimAst: Ue,
        emStrongLDelim: Fe,
        url: h(
          /^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,
          "i"
        )
          .replace(
            "email",
            /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/
          )
          .getRegex(),
        _backpedal:
          /(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,
        del: /^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,
        text: /^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/,
      },
      nt = {
        ...j,
        br: h(le).replace("{2,}", "*").getRegex(),
        text: h(j.text)
          .replace("\\b_", "\\b_| {2,}\\n")
          .replace(/\{2,\}/g, "*")
          .getRegex(),
      },
      B = { normal: W, gfm: Ie, pedantic: Be },
      M = { normal: J, gfm: j, breaks: nt, pedantic: tt };
    var rt = {
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#39;",
      },
      ge = (a) => rt[a];
    function w(a, e) {
      if (e) {
        if (m.escapeTest.test(a)) return a.replace(m.escapeReplace, ge);
      } else if (m.escapeTestNoEncode.test(a))
        return a.replace(m.escapeReplaceNoEncode, ge);
      return a;
    }
    function V(a) {
      try {
        a = encodeURI(a).replace(m.percentDecode, "%");
      } catch {
        return null;
      }
      return a;
    }
    function Y(a, e) {
      let t = a.replace(m.findPipe, (i, s, o) => {
          let l = !1,
            u = s;
          for (; --u >= 0 && o[u] === "\\"; ) l = !l;
          return l ? "|" : " |";
        }),
        n = t.split(m.splitPipe),
        r = 0;
      if (
        (n[0].trim() || n.shift(),
        n.length > 0 && !n.at(-1)?.trim() && n.pop(),
        e)
      )
        if (n.length > e) n.splice(e);
        else for (; n.length < e; ) n.push("");
      for (; r < n.length; r++) n[r] = n[r].trim().replace(m.slashPipe, "|");
      return n;
    }
    function z(a, e, t) {
      let n = a.length;
      if (n === 0) return "";
      let r = 0;
      for (; r < n; ) {
        let i = a.charAt(n - r - 1);
        if (i === e && !t) r++;
        else if (i !== e && t) r++;
        else break;
      }
      return a.slice(0, n - r);
    }
    function fe(a, e) {
      if (a.indexOf(e[1]) === -1) return -1;
      let t = 0;
      for (let n = 0; n < a.length; n++)
        if (a[n] === "\\") n++;
        else if (a[n] === e[0]) t++;
        else if (a[n] === e[1] && (t--, t < 0)) return n;
      return t > 0 ? -2 : -1;
    }
    function me(a, e, t, n, r) {
      let i = e.href,
        s = e.title || null,
        o = a[1].replace(r.other.outputLinkReplace, "$1");
      n.state.inLink = !0;
      let l = {
        type: a[0].charAt(0) === "!" ? "image" : "link",
        raw: t,
        href: i,
        title: s,
        text: o,
        tokens: n.inlineTokens(o),
      };
      return (n.state.inLink = !1), l;
    }
    function st(a, e, t) {
      let n = a.match(t.other.indentCodeCompensation);
      if (n === null) return e;
      let r = n[1];
      return e
        .split(
          `
`
        )
        .map((i) => {
          let s = i.match(t.other.beginningSpace);
          if (s === null) return i;
          let [o] = s;
          return o.length >= r.length ? i.slice(r.length) : i;
        }).join(`
`);
    }
    var y = class {
      options;
      rules;
      lexer;
      constructor(e) {
        this.options = e || O;
      }
      space(e) {
        let t = this.rules.block.newline.exec(e);
        if (t && t[0].length > 0) return { type: "space", raw: t[0] };
      }
      code(e) {
        let t = this.rules.block.code.exec(e);
        if (t) {
          let n = t[0].replace(this.rules.other.codeRemoveIndent, "");
          return {
            type: "code",
            raw: t[0],
            codeBlockStyle: "indented",
            text: this.options.pedantic
              ? n
              : z(
                  n,
                  `
`
                ),
          };
        }
      }
      fences(e) {
        let t = this.rules.block.fences.exec(e);
        if (t) {
          let n = t[0],
            r = st(n, t[3] || "", this.rules);
          return {
            type: "code",
            raw: n,
            lang: t[2]
              ? t[2].trim().replace(this.rules.inline.anyPunctuation, "$1")
              : t[2],
            text: r,
          };
        }
      }
      heading(e) {
        let t = this.rules.block.heading.exec(e);
        if (t) {
          let n = t[2].trim();
          if (this.rules.other.endingHash.test(n)) {
            let r = z(n, "#");
            (this.options.pedantic ||
              !r ||
              this.rules.other.endingSpaceChar.test(r)) &&
              (n = r.trim());
          }
          return {
            type: "heading",
            raw: t[0],
            depth: t[1].length,
            text: n,
            tokens: this.lexer.inline(n),
          };
        }
      }
      hr(e) {
        let t = this.rules.block.hr.exec(e);
        if (t)
          return {
            type: "hr",
            raw: z(
              t[0],
              `
`
            ),
          };
      }
      blockquote(e) {
        let t = this.rules.block.blockquote.exec(e);
        if (t) {
          let n = z(
              t[0],
              `
`
            ).split(`
`),
            r = "",
            i = "",
            s = [];
          for (; n.length > 0; ) {
            let o = !1,
              l = [],
              u;
            for (u = 0; u < n.length; u++)
              if (this.rules.other.blockquoteStart.test(n[u]))
                l.push(n[u]), (o = !0);
              else if (!o) l.push(n[u]);
              else break;
            n = n.slice(u);
            let p = l.join(`
`),
              c = p
                .replace(
                  this.rules.other.blockquoteSetextReplace,
                  `
    $1`
                )
                .replace(this.rules.other.blockquoteSetextReplace2, "");
            (r = r
              ? `${r}
${p}`
              : p),
              (i = i
                ? `${i}
${c}`
                : c);
            let f = this.lexer.state.top;
            if (
              ((this.lexer.state.top = !0),
              this.lexer.blockTokens(c, s, !0),
              (this.lexer.state.top = f),
              n.length === 0)
            )
              break;
            let k = s.at(-1);
            if (k?.type === "code") break;
            if (k?.type === "blockquote") {
              let R = k,
                g =
                  R.raw +
                  `
` +
                  n.join(`
`),
                T = this.blockquote(g);
              (s[s.length - 1] = T),
                (r = r.substring(0, r.length - R.raw.length) + T.raw),
                (i = i.substring(0, i.length - R.text.length) + T.text);
              break;
            } else if (k?.type === "list") {
              let R = k,
                g =
                  R.raw +
                  `
` +
                  n.join(`
`),
                T = this.list(g);
              (s[s.length - 1] = T),
                (r = r.substring(0, r.length - k.raw.length) + T.raw),
                (i = i.substring(0, i.length - R.raw.length) + T.raw),
                (n = g.substring(s.at(-1).raw.length).split(`
`));
              continue;
            }
          }
          return { type: "blockquote", raw: r, tokens: s, text: i };
        }
      }
      list(e) {
        let t = this.rules.block.list.exec(e);
        if (t) {
          let n = t[1].trim(),
            r = n.length > 1,
            i = {
              type: "list",
              raw: "",
              ordered: r,
              start: r ? +n.slice(0, -1) : "",
              loose: !1,
              items: [],
            };
          (n = r ? `\\d{1,9}\\${n.slice(-1)}` : `\\${n}`),
            this.options.pedantic && (n = r ? n : "[*+-]");
          let s = this.rules.other.listItemRegex(n),
            o = !1;
          for (; e; ) {
            let u = !1,
              p = "",
              c = "";
            if (!(t = s.exec(e)) || this.rules.block.hr.test(e)) break;
            (p = t[0]), (e = e.substring(p.length));
            let f = t[2]
                .split(
                  `
`,
                  1
                )[0]
                .replace(this.rules.other.listReplaceTabs, (Z) =>
                  " ".repeat(3 * Z.length)
                ),
              k = e.split(
                `
`,
                1
              )[0],
              R = !f.trim(),
              g = 0;
            if (
              (this.options.pedantic
                ? ((g = 2), (c = f.trimStart()))
                : R
                ? (g = t[1].length + 1)
                : ((g = t[2].search(this.rules.other.nonSpaceChar)),
                  (g = g > 4 ? 1 : g),
                  (c = f.slice(g)),
                  (g += t[1].length)),
              R &&
                this.rules.other.blankLine.test(k) &&
                ((p +=
                  k +
                  `
`),
                (e = e.substring(k.length + 1)),
                (u = !0)),
              !u)
            ) {
              let Z = this.rules.other.nextBulletRegex(g),
                te = this.rules.other.hrRegex(g),
                ne = this.rules.other.fencesBeginRegex(g),
                re = this.rules.other.headingBeginRegex(g),
                xe = this.rules.other.htmlBeginRegex(g);
              for (; e; ) {
                let G = e.split(
                    `
`,
                    1
                  )[0],
                  E;
                if (
                  ((k = G),
                  this.options.pedantic
                    ? ((k = k.replace(
                        this.rules.other.listReplaceNesting,
                        "  "
                      )),
                      (E = k))
                    : (E = k.replace(this.rules.other.tabCharGlobal, "    ")),
                  ne.test(k) ||
                    re.test(k) ||
                    xe.test(k) ||
                    Z.test(k) ||
                    te.test(k))
                )
                  break;
                if (E.search(this.rules.other.nonSpaceChar) >= g || !k.trim())
                  c +=
                    `
` + E.slice(g);
                else {
                  if (
                    R ||
                    f
                      .replace(this.rules.other.tabCharGlobal, "    ")
                      .search(this.rules.other.nonSpaceChar) >= 4 ||
                    ne.test(f) ||
                    re.test(f) ||
                    te.test(f)
                  )
                    break;
                  c +=
                    `
` + k;
                }
                !R && !k.trim() && (R = !0),
                  (p +=
                    G +
                    `
`),
                  (e = e.substring(G.length + 1)),
                  (f = E.slice(g));
              }
            }
            i.loose ||
              (o
                ? (i.loose = !0)
                : this.rules.other.doubleBlankLine.test(p) && (o = !0));
            let T = null,
              ee;
            this.options.gfm &&
              ((T = this.rules.other.listIsTask.exec(c)),
              T &&
                ((ee = T[0] !== "[ ] "),
                (c = c.replace(this.rules.other.listReplaceTask, "")))),
              i.items.push({
                type: "list_item",
                raw: p,
                task: !!T,
                checked: ee,
                loose: !1,
                text: c,
                tokens: [],
              }),
              (i.raw += p);
          }
          let l = i.items.at(-1);
          if (l) (l.raw = l.raw.trimEnd()), (l.text = l.text.trimEnd());
          else return;
          i.raw = i.raw.trimEnd();
          for (let u = 0; u < i.items.length; u++)
            if (
              ((this.lexer.state.top = !1),
              (i.items[u].tokens = this.lexer.blockTokens(i.items[u].text, [])),
              !i.loose)
            ) {
              let p = i.items[u].tokens.filter((f) => f.type === "space"),
                c =
                  p.length > 0 &&
                  p.some((f) => this.rules.other.anyLine.test(f.raw));
              i.loose = c;
            }
          if (i.loose)
            for (let u = 0; u < i.items.length; u++) i.items[u].loose = !0;
          return i;
        }
      }
      html(e) {
        let t = this.rules.block.html.exec(e);
        if (t)
          return {
            type: "html",
            block: !0,
            raw: t[0],
            pre: t[1] === "pre" || t[1] === "script" || t[1] === "style",
            text: t[0],
          };
      }
      def(e) {
        let t = this.rules.block.def.exec(e);
        if (t) {
          let n = t[1]
              .toLowerCase()
              .replace(this.rules.other.multipleSpaceGlobal, " "),
            r = t[2]
              ? t[2]
                  .replace(this.rules.other.hrefBrackets, "$1")
                  .replace(this.rules.inline.anyPunctuation, "$1")
              : "",
            i = t[3]
              ? t[3]
                  .substring(1, t[3].length - 1)
                  .replace(this.rules.inline.anyPunctuation, "$1")
              : t[3];
          return { type: "def", tag: n, raw: t[0], href: r, title: i };
        }
      }
      table(e) {
        let t = this.rules.block.table.exec(e);
        if (!t || !this.rules.other.tableDelimiter.test(t[2])) return;
        let n = Y(t[1]),
          r = t[2].replace(this.rules.other.tableAlignChars, "").split("|"),
          i = t[3]?.trim()
            ? t[3].replace(this.rules.other.tableRowBlankLine, "").split(`
`)
            : [],
          s = { type: "table", raw: t[0], header: [], align: [], rows: [] };
        if (n.length === r.length) {
          for (let o of r)
            this.rules.other.tableAlignRight.test(o)
              ? s.align.push("right")
              : this.rules.other.tableAlignCenter.test(o)
              ? s.align.push("center")
              : this.rules.other.tableAlignLeft.test(o)
              ? s.align.push("left")
              : s.align.push(null);
          for (let o = 0; o < n.length; o++)
            s.header.push({
              text: n[o],
              tokens: this.lexer.inline(n[o]),
              header: !0,
              align: s.align[o],
            });
          for (let o of i)
            s.rows.push(
              Y(o, s.header.length).map((l, u) => ({
                text: l,
                tokens: this.lexer.inline(l),
                header: !1,
                align: s.align[u],
              }))
            );
          return s;
        }
      }
      lheading(e) {
        let t = this.rules.block.lheading.exec(e);
        if (t)
          return {
            type: "heading",
            raw: t[0],
            depth: t[2].charAt(0) === "=" ? 1 : 2,
            text: t[1],
            tokens: this.lexer.inline(t[1]),
          };
      }
      paragraph(e) {
        let t = this.rules.block.paragraph.exec(e);
        if (t) {
          let n =
            t[1].charAt(t[1].length - 1) ===
            `
`
              ? t[1].slice(0, -1)
              : t[1];
          return {
            type: "paragraph",
            raw: t[0],
            text: n,
            tokens: this.lexer.inline(n),
          };
        }
      }
      text(e) {
        let t = this.rules.block.text.exec(e);
        if (t)
          return {
            type: "text",
            raw: t[0],
            text: t[0],
            tokens: this.lexer.inline(t[0]),
          };
      }
      escape(e) {
        let t = this.rules.inline.escape.exec(e);
        if (t) return { type: "escape", raw: t[0], text: t[1] };
      }
      tag(e) {
        let t = this.rules.inline.tag.exec(e);
        if (t)
          return (
            !this.lexer.state.inLink && this.rules.other.startATag.test(t[0])
              ? (this.lexer.state.inLink = !0)
              : this.lexer.state.inLink &&
                this.rules.other.endATag.test(t[0]) &&
                (this.lexer.state.inLink = !1),
            !this.lexer.state.inRawBlock &&
            this.rules.other.startPreScriptTag.test(t[0])
              ? (this.lexer.state.inRawBlock = !0)
              : this.lexer.state.inRawBlock &&
                this.rules.other.endPreScriptTag.test(t[0]) &&
                (this.lexer.state.inRawBlock = !1),
            {
              type: "html",
              raw: t[0],
              inLink: this.lexer.state.inLink,
              inRawBlock: this.lexer.state.inRawBlock,
              block: !1,
              text: t[0],
            }
          );
      }
      link(e) {
        let t = this.rules.inline.link.exec(e);
        if (t) {
          let n = t[2].trim();
          if (
            !this.options.pedantic &&
            this.rules.other.startAngleBracket.test(n)
          ) {
            if (!this.rules.other.endAngleBracket.test(n)) return;
            let s = z(n.slice(0, -1), "\\");
            if ((n.length - s.length) % 2 === 0) return;
          } else {
            let s = fe(t[2], "()");
            if (s === -2) return;
            if (s > -1) {
              let l = (t[0].indexOf("!") === 0 ? 5 : 4) + t[1].length + s;
              (t[2] = t[2].substring(0, s)),
                (t[0] = t[0].substring(0, l).trim()),
                (t[3] = "");
            }
          }
          let r = t[2],
            i = "";
          if (this.options.pedantic) {
            let s = this.rules.other.pedanticHrefTitle.exec(r);
            s && ((r = s[1]), (i = s[3]));
          } else i = t[3] ? t[3].slice(1, -1) : "";
          return (
            (r = r.trim()),
            this.rules.other.startAngleBracket.test(r) &&
              (this.options.pedantic &&
              !this.rules.other.endAngleBracket.test(n)
                ? (r = r.slice(1))
                : (r = r.slice(1, -1))),
            me(
              t,
              {
                href: r && r.replace(this.rules.inline.anyPunctuation, "$1"),
                title: i && i.replace(this.rules.inline.anyPunctuation, "$1"),
              },
              t[0],
              this.lexer,
              this.rules
            )
          );
        }
      }
      reflink(e, t) {
        let n;
        if (
          (n = this.rules.inline.reflink.exec(e)) ||
          (n = this.rules.inline.nolink.exec(e))
        ) {
          let r = (n[2] || n[1]).replace(
              this.rules.other.multipleSpaceGlobal,
              " "
            ),
            i = t[r.toLowerCase()];
          if (!i) {
            let s = n[0].charAt(0);
            return { type: "text", raw: s, text: s };
          }
          return me(n, i, n[0], this.lexer, this.rules);
        }
      }
      emStrong(e, t, n = "") {
        let r = this.rules.inline.emStrongLDelim.exec(e);
        if (!r || (r[3] && n.match(this.rules.other.unicodeAlphaNumeric)))
          return;
        if (
          !(r[1] || r[2] || "") ||
          !n ||
          this.rules.inline.punctuation.exec(n)
        ) {
          let s = [...r[0]].length - 1,
            o,
            l,
            u = s,
            p = 0,
            c =
              r[0][0] === "*"
                ? this.rules.inline.emStrongRDelimAst
                : this.rules.inline.emStrongRDelimUnd;
          for (
            c.lastIndex = 0, t = t.slice(-1 * e.length + s);
            (r = c.exec(t)) != null;

          ) {
            if (((o = r[1] || r[2] || r[3] || r[4] || r[5] || r[6]), !o))
              continue;
            if (((l = [...o].length), r[3] || r[4])) {
              u += l;
              continue;
            } else if ((r[5] || r[6]) && s % 3 && !((s + l) % 3)) {
              p += l;
              continue;
            }
            if (((u -= l), u > 0)) continue;
            l = Math.min(l, l + u + p);
            let f = [...r[0]][0].length,
              k = e.slice(0, s + r.index + f + l);
            if (Math.min(s, l) % 2) {
              let g = k.slice(1, -1);
              return {
                type: "em",
                raw: k,
                text: g,
                tokens: this.lexer.inlineTokens(g),
              };
            }
            let R = k.slice(2, -2);
            return {
              type: "strong",
              raw: k,
              text: R,
              tokens: this.lexer.inlineTokens(R),
            };
          }
        }
      }
      codespan(e) {
        let t = this.rules.inline.code.exec(e);
        if (t) {
          let n = t[2].replace(this.rules.other.newLineCharGlobal, " "),
            r = this.rules.other.nonSpaceChar.test(n),
            i =
              this.rules.other.startingSpaceChar.test(n) &&
              this.rules.other.endingSpaceChar.test(n);
          return (
            r && i && (n = n.substring(1, n.length - 1)),
            { type: "codespan", raw: t[0], text: n }
          );
        }
      }
      br(e) {
        let t = this.rules.inline.br.exec(e);
        if (t) return { type: "br", raw: t[0] };
      }
      del(e) {
        let t = this.rules.inline.del.exec(e);
        if (t)
          return {
            type: "del",
            raw: t[0],
            text: t[2],
            tokens: this.lexer.inlineTokens(t[2]),
          };
      }
      autolink(e) {
        let t = this.rules.inline.autolink.exec(e);
        if (t) {
          let n, r;
          return (
            t[2] === "@"
              ? ((n = t[1]), (r = "mailto:" + n))
              : ((n = t[1]), (r = n)),
            {
              type: "link",
              raw: t[0],
              text: n,
              href: r,
              tokens: [{ type: "text", raw: n, text: n }],
            }
          );
        }
      }
      url(e) {
        let t;
        if ((t = this.rules.inline.url.exec(e))) {
          let n, r;
          if (t[2] === "@") (n = t[0]), (r = "mailto:" + n);
          else {
            let i;
            do
              (i = t[0]),
                (t[0] = this.rules.inline._backpedal.exec(t[0])?.[0] ?? "");
            while (i !== t[0]);
            (n = t[0]), t[1] === "www." ? (r = "http://" + t[0]) : (r = t[0]);
          }
          return {
            type: "link",
            raw: t[0],
            text: n,
            href: r,
            tokens: [{ type: "text", raw: n, text: n }],
          };
        }
      }
      inlineText(e) {
        let t = this.rules.inline.text.exec(e);
        if (t) {
          let n = this.lexer.state.inRawBlock;
          return { type: "text", raw: t[0], text: t[0], escaped: n };
        }
      }
    };
    var x = class a {
      tokens;
      options;
      state;
      tokenizer;
      inlineQueue;
      constructor(e) {
        (this.tokens = []),
          (this.tokens.links = Object.create(null)),
          (this.options = e || O),
          (this.options.tokenizer = this.options.tokenizer || new y()),
          (this.tokenizer = this.options.tokenizer),
          (this.tokenizer.options = this.options),
          (this.tokenizer.lexer = this),
          (this.inlineQueue = []),
          (this.state = { inLink: !1, inRawBlock: !1, top: !0 });
        let t = { other: m, block: B.normal, inline: M.normal };
        this.options.pedantic
          ? ((t.block = B.pedantic), (t.inline = M.pedantic))
          : this.options.gfm &&
            ((t.block = B.gfm),
            this.options.breaks ? (t.inline = M.breaks) : (t.inline = M.gfm)),
          (this.tokenizer.rules = t);
      }
      static get rules() {
        return { block: B, inline: M };
      }
      static lex(e, t) {
        return new a(t).lex(e);
      }
      static lexInline(e, t) {
        return new a(t).inlineTokens(e);
      }
      lex(e) {
        (e = e.replace(
          m.carriageReturn,
          `
`
        )),
          this.blockTokens(e, this.tokens);
        for (let t = 0; t < this.inlineQueue.length; t++) {
          let n = this.inlineQueue[t];
          this.inlineTokens(n.src, n.tokens);
        }
        return (this.inlineQueue = []), this.tokens;
      }
      blockTokens(e, t = [], n = !1) {
        for (
          this.options.pedantic &&
          (e = e.replace(m.tabCharGlobal, "    ").replace(m.spaceLine, ""));
          e;

        ) {
          let r;
          if (
            this.options.extensions?.block?.some((s) =>
              (r = s.call({ lexer: this }, e, t))
                ? ((e = e.substring(r.raw.length)), t.push(r), !0)
                : !1
            )
          )
            continue;
          if ((r = this.tokenizer.space(e))) {
            e = e.substring(r.raw.length);
            let s = t.at(-1);
            r.raw.length === 1 && s !== void 0
              ? (s.raw += `
`)
              : t.push(r);
            continue;
          }
          if ((r = this.tokenizer.code(e))) {
            e = e.substring(r.raw.length);
            let s = t.at(-1);
            s?.type === "paragraph" || s?.type === "text"
              ? ((s.raw +=
                  (s.raw.endsWith(`
`)
                    ? ""
                    : `
`) + r.raw),
                (s.text +=
                  `
` + r.text),
                (this.inlineQueue.at(-1).src = s.text))
              : t.push(r);
            continue;
          }
          if ((r = this.tokenizer.fences(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.heading(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.hr(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.blockquote(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.list(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.html(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.def(e))) {
            e = e.substring(r.raw.length);
            let s = t.at(-1);
            s?.type === "paragraph" || s?.type === "text"
              ? ((s.raw +=
                  (s.raw.endsWith(`
`)
                    ? ""
                    : `
`) + r.raw),
                (s.text +=
                  `
` + r.raw),
                (this.inlineQueue.at(-1).src = s.text))
              : this.tokens.links[r.tag] ||
                (this.tokens.links[r.tag] = { href: r.href, title: r.title });
            continue;
          }
          if ((r = this.tokenizer.table(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          if ((r = this.tokenizer.lheading(e))) {
            (e = e.substring(r.raw.length)), t.push(r);
            continue;
          }
          let i = e;
          if (this.options.extensions?.startBlock) {
            let s = 1 / 0,
              o = e.slice(1),
              l;
            this.options.extensions.startBlock.forEach((u) => {
              (l = u.call({ lexer: this }, o)),
                typeof l == "number" && l >= 0 && (s = Math.min(s, l));
            }),
              s < 1 / 0 && s >= 0 && (i = e.substring(0, s + 1));
          }
          if (this.state.top && (r = this.tokenizer.paragraph(i))) {
            let s = t.at(-1);
            n && s?.type === "paragraph"
              ? ((s.raw +=
                  (s.raw.endsWith(`
`)
                    ? ""
                    : `
`) + r.raw),
                (s.text +=
                  `
` + r.text),
                this.inlineQueue.pop(),
                (this.inlineQueue.at(-1).src = s.text))
              : t.push(r),
              (n = i.length !== e.length),
              (e = e.substring(r.raw.length));
            continue;
          }
          if ((r = this.tokenizer.text(e))) {
            e = e.substring(r.raw.length);
            let s = t.at(-1);
            s?.type === "text"
              ? ((s.raw +=
                  (s.raw.endsWith(`
`)
                    ? ""
                    : `
`) + r.raw),
                (s.text +=
                  `
` + r.text),
                this.inlineQueue.pop(),
                (this.inlineQueue.at(-1).src = s.text))
              : t.push(r);
            continue;
          }
          if (e) {
            let s = "Infinite loop on byte: " + e.charCodeAt(0);
            if (this.options.silent) {
              console.error(s);
              break;
            } else throw new Error(s);
          }
        }
        return (this.state.top = !0), t;
      }
      inline(e, t = []) {
        return this.inlineQueue.push({ src: e, tokens: t }), t;
      }
      inlineTokens(e, t = []) {
        let n = e,
          r = null;
        if (this.tokens.links) {
          let o = Object.keys(this.tokens.links);
          if (o.length > 0)
            for (
              ;
              (r = this.tokenizer.rules.inline.reflinkSearch.exec(n)) != null;

            )
              o.includes(r[0].slice(r[0].lastIndexOf("[") + 1, -1)) &&
                (n =
                  n.slice(0, r.index) +
                  "[" +
                  "a".repeat(r[0].length - 2) +
                  "]" +
                  n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex));
        }
        for (
          ;
          (r = this.tokenizer.rules.inline.anyPunctuation.exec(n)) != null;

        )
          n =
            n.slice(0, r.index) +
            "++" +
            n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);
        for (; (r = this.tokenizer.rules.inline.blockSkip.exec(n)) != null; )
          n =
            n.slice(0, r.index) +
            "[" +
            "a".repeat(r[0].length - 2) +
            "]" +
            n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);
        let i = !1,
          s = "";
        for (; e; ) {
          i || (s = ""), (i = !1);
          let o;
          if (
            this.options.extensions?.inline?.some((u) =>
              (o = u.call({ lexer: this }, e, t))
                ? ((e = e.substring(o.raw.length)), t.push(o), !0)
                : !1
            )
          )
            continue;
          if ((o = this.tokenizer.escape(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.tag(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.link(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.reflink(e, this.tokens.links))) {
            e = e.substring(o.raw.length);
            let u = t.at(-1);
            o.type === "text" && u?.type === "text"
              ? ((u.raw += o.raw), (u.text += o.text))
              : t.push(o);
            continue;
          }
          if ((o = this.tokenizer.emStrong(e, n, s))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.codespan(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.br(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.del(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if ((o = this.tokenizer.autolink(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          if (!this.state.inLink && (o = this.tokenizer.url(e))) {
            (e = e.substring(o.raw.length)), t.push(o);
            continue;
          }
          let l = e;
          if (this.options.extensions?.startInline) {
            let u = 1 / 0,
              p = e.slice(1),
              c;
            this.options.extensions.startInline.forEach((f) => {
              (c = f.call({ lexer: this }, p)),
                typeof c == "number" && c >= 0 && (u = Math.min(u, c));
            }),
              u < 1 / 0 && u >= 0 && (l = e.substring(0, u + 1));
          }
          if ((o = this.tokenizer.inlineText(l))) {
            (e = e.substring(o.raw.length)),
              o.raw.slice(-1) !== "_" && (s = o.raw.slice(-1)),
              (i = !0);
            let u = t.at(-1);
            u?.type === "text"
              ? ((u.raw += o.raw), (u.text += o.text))
              : t.push(o);
            continue;
          }
          if (e) {
            let u = "Infinite loop on byte: " + e.charCodeAt(0);
            if (this.options.silent) {
              console.error(u);
              break;
            } else throw new Error(u);
          }
        }
        return t;
      }
    };
    var P = class {
      options;
      parser;
      constructor(e) {
        this.options = e || O;
      }
      space(e) {
        return "";
      }
      code({ text: e, lang: t, escaped: n }) {
        let r = (t || "").match(m.notSpaceStart)?.[0],
          i =
            e.replace(m.endingNewline, "") +
            `
`;
        return r
          ? '<pre><code class="language-' +
              w(r) +
              '">' +
              (n ? i : w(i, !0)) +
              `</code></pre>
`
          : "<pre><code>" +
              (n ? i : w(i, !0)) +
              `</code></pre>
`;
      }
      blockquote({ tokens: e }) {
        return `<blockquote>
${this.parser.parse(e)}</blockquote>
`;
      }
      html({ text: e }) {
        return e;
      }
      heading({ tokens: e, depth: t }) {
        return `<h${t}>${this.parser.parseInline(e)}</h${t}>
`;
      }
      hr(e) {
        return `<hr>
`;
      }
      list(e) {
        let t = e.ordered,
          n = e.start,
          r = "";
        for (let o = 0; o < e.items.length; o++) {
          let l = e.items[o];
          r += this.listitem(l);
        }
        let i = t ? "ol" : "ul",
          s = t && n !== 1 ? ' start="' + n + '"' : "";
        return (
          "<" +
          i +
          s +
          `>
` +
          r +
          "</" +
          i +
          `>
`
        );
      }
      listitem(e) {
        let t = "";
        if (e.task) {
          let n = this.checkbox({ checked: !!e.checked });
          e.loose
            ? e.tokens[0]?.type === "paragraph"
              ? ((e.tokens[0].text = n + " " + e.tokens[0].text),
                e.tokens[0].tokens &&
                  e.tokens[0].tokens.length > 0 &&
                  e.tokens[0].tokens[0].type === "text" &&
                  ((e.tokens[0].tokens[0].text =
                    n + " " + w(e.tokens[0].tokens[0].text)),
                  (e.tokens[0].tokens[0].escaped = !0)))
              : e.tokens.unshift({
                  type: "text",
                  raw: n + " ",
                  text: n + " ",
                  escaped: !0,
                })
            : (t += n + " ");
        }
        return (
          (t += this.parser.parse(e.tokens, !!e.loose)),
          `<li>${t}</li>
`
        );
      }
      checkbox({ checked: e }) {
        return (
          "<input " + (e ? 'checked="" ' : "") + 'disabled="" type="checkbox">'
        );
      }
      paragraph({ tokens: e }) {
        return `<p>${this.parser.parseInline(e)}</p>
`;
      }
      table(e) {
        let t = "",
          n = "";
        for (let i = 0; i < e.header.length; i++)
          n += this.tablecell(e.header[i]);
        t += this.tablerow({ text: n });
        let r = "";
        for (let i = 0; i < e.rows.length; i++) {
          let s = e.rows[i];
          n = "";
          for (let o = 0; o < s.length; o++) n += this.tablecell(s[o]);
          r += this.tablerow({ text: n });
        }
        return (
          r && (r = `<tbody>${r}</tbody>`),
          `<table>
<thead>
` +
            t +
            `</thead>
` +
            r +
            `</table>
`
        );
      }
      tablerow({ text: e }) {
        return `<tr>
${e}</tr>
`;
      }
      tablecell(e) {
        let t = this.parser.parseInline(e.tokens),
          n = e.header ? "th" : "td";
        return (
          (e.align ? `<${n} align="${e.align}">` : `<${n}>`) +
          t +
          `</${n}>
`
        );
      }
      strong({ tokens: e }) {
        return `<strong>${this.parser.parseInline(e)}</strong>`;
      }
      em({ tokens: e }) {
        return `<em>${this.parser.parseInline(e)}</em>`;
      }
      codespan({ text: e }) {
        return `<code>${w(e, !0)}</code>`;
      }
      br(e) {
        return "<br>";
      }
      del({ tokens: e }) {
        return `<del>${this.parser.parseInline(e)}</del>`;
      }
      link({ href: e, title: t, tokens: n }) {
        let r = this.parser.parseInline(n),
          i = V(e);
        if (i === null) return r;
        e = i;
        let s = '<a href="' + e + '"';
        return t && (s += ' title="' + w(t) + '"'), (s += ">" + r + "</a>"), s;
      }
      image({ href: e, title: t, text: n, tokens: r }) {
        r && (n = this.parser.parseInline(r, this.parser.textRenderer));
        let i = V(e);
        if (i === null) return w(n);
        e = i;
        let s = `<img src="${e}" alt="${n}"`;
        return t && (s += ` title="${w(t)}"`), (s += ">"), s;
      }
      text(e) {
        return "tokens" in e && e.tokens
          ? this.parser.parseInline(e.tokens)
          : "escaped" in e && e.escaped
          ? e.text
          : w(e.text);
      }
    };
    var S = class {
      strong({ text: e }) {
        return e;
      }
      em({ text: e }) {
        return e;
      }
      codespan({ text: e }) {
        return e;
      }
      del({ text: e }) {
        return e;
      }
      html({ text: e }) {
        return e;
      }
      text({ text: e }) {
        return e;
      }
      link({ text: e }) {
        return "" + e;
      }
      image({ text: e }) {
        return "" + e;
      }
      br() {
        return "";
      }
    };
    var b = class a {
      options;
      renderer;
      textRenderer;
      constructor(e) {
        (this.options = e || O),
          (this.options.renderer = this.options.renderer || new P()),
          (this.renderer = this.options.renderer),
          (this.renderer.options = this.options),
          (this.renderer.parser = this),
          (this.textRenderer = new S());
      }
      static parse(e, t) {
        return new a(t).parse(e);
      }
      static parseInline(e, t) {
        return new a(t).parseInline(e);
      }
      parse(e, t = !0) {
        let n = "";
        for (let r = 0; r < e.length; r++) {
          let i = e[r];
          if (this.options.extensions?.renderers?.[i.type]) {
            let o = i,
              l = this.options.extensions.renderers[o.type].call(
                { parser: this },
                o
              );
            if (
              l !== !1 ||
              ![
                "space",
                "hr",
                "heading",
                "code",
                "table",
                "blockquote",
                "list",
                "html",
                "paragraph",
                "text",
              ].includes(o.type)
            ) {
              n += l || "";
              continue;
            }
          }
          let s = i;
          switch (s.type) {
            case "space": {
              n += this.renderer.space(s);
              continue;
            }
            case "hr": {
              n += this.renderer.hr(s);
              continue;
            }
            case "heading": {
              n += this.renderer.heading(s);
              continue;
            }
            case "code": {
              n += this.renderer.code(s);
              continue;
            }
            case "table": {
              n += this.renderer.table(s);
              continue;
            }
            case "blockquote": {
              n += this.renderer.blockquote(s);
              continue;
            }
            case "list": {
              n += this.renderer.list(s);
              continue;
            }
            case "html": {
              n += this.renderer.html(s);
              continue;
            }
            case "paragraph": {
              n += this.renderer.paragraph(s);
              continue;
            }
            case "text": {
              let o = s,
                l = this.renderer.text(o);
              for (; r + 1 < e.length && e[r + 1].type === "text"; )
                (o = e[++r]),
                  (l +=
                    `
` + this.renderer.text(o));
              t
                ? (n += this.renderer.paragraph({
                    type: "paragraph",
                    raw: l,
                    text: l,
                    tokens: [{ type: "text", raw: l, text: l, escaped: !0 }],
                  }))
                : (n += l);
              continue;
            }
            default: {
              let o = 'Token with "' + s.type + '" type was not found.';
              if (this.options.silent) return console.error(o), "";
              throw new Error(o);
            }
          }
        }
        return n;
      }
      parseInline(e, t = this.renderer) {
        let n = "";
        for (let r = 0; r < e.length; r++) {
          let i = e[r];
          if (this.options.extensions?.renderers?.[i.type]) {
            let o = this.options.extensions.renderers[i.type].call(
              { parser: this },
              i
            );
            if (
              o !== !1 ||
              ![
                "escape",
                "html",
                "link",
                "image",
                "strong",
                "em",
                "codespan",
                "br",
                "del",
                "text",
              ].includes(i.type)
            ) {
              n += o || "";
              continue;
            }
          }
          let s = i;
          switch (s.type) {
            case "escape": {
              n += t.text(s);
              break;
            }
            case "html": {
              n += t.html(s);
              break;
            }
            case "link": {
              n += t.link(s);
              break;
            }
            case "image": {
              n += t.image(s);
              break;
            }
            case "strong": {
              n += t.strong(s);
              break;
            }
            case "em": {
              n += t.em(s);
              break;
            }
            case "codespan": {
              n += t.codespan(s);
              break;
            }
            case "br": {
              n += t.br(s);
              break;
            }
            case "del": {
              n += t.del(s);
              break;
            }
            case "text": {
              n += t.text(s);
              break;
            }
            default: {
              let o = 'Token with "' + s.type + '" type was not found.';
              if (this.options.silent) return console.error(o), "";
              throw new Error(o);
            }
          }
        }
        return n;
      }
    };
    var $ = class {
      options;
      block;
      constructor(e) {
        this.options = e || O;
      }
      static passThroughHooks = new Set([
        "preprocess",
        "postprocess",
        "processAllTokens",
      ]);
      preprocess(e) {
        return e;
      }
      postprocess(e) {
        return e;
      }
      processAllTokens(e) {
        return e;
      }
      provideLexer() {
        return this.block ? x.lex : x.lexInline;
      }
      provideParser() {
        return this.block ? b.parse : b.parseInline;
      }
    };
    var A = class {
      defaults = _();
      options = this.setOptions;
      parse = this.parseMarkdown(!0);
      parseInline = this.parseMarkdown(!1);
      Parser = b;
      Renderer = P;
      TextRenderer = S;
      Lexer = x;
      Tokenizer = y;
      Hooks = $;
      constructor(...e) {
        this.use(...e);
      }
      walkTokens(e, t) {
        let n = [];
        for (let r of e)
          switch (((n = n.concat(t.call(this, r))), r.type)) {
            case "table": {
              let i = r;
              for (let s of i.header)
                n = n.concat(this.walkTokens(s.tokens, t));
              for (let s of i.rows)
                for (let o of s) n = n.concat(this.walkTokens(o.tokens, t));
              break;
            }
            case "list": {
              let i = r;
              n = n.concat(this.walkTokens(i.items, t));
              break;
            }
            default: {
              let i = r;
              this.defaults.extensions?.childTokens?.[i.type]
                ? this.defaults.extensions.childTokens[i.type].forEach((s) => {
                    let o = i[s].flat(1 / 0);
                    n = n.concat(this.walkTokens(o, t));
                  })
                : i.tokens && (n = n.concat(this.walkTokens(i.tokens, t)));
            }
          }
        return n;
      }
      use(...e) {
        let t = this.defaults.extensions || { renderers: {}, childTokens: {} };
        return (
          e.forEach((n) => {
            let r = { ...n };
            if (
              ((r.async = this.defaults.async || r.async || !1),
              n.extensions &&
                (n.extensions.forEach((i) => {
                  if (!i.name) throw new Error("extension name required");
                  if ("renderer" in i) {
                    let s = t.renderers[i.name];
                    s
                      ? (t.renderers[i.name] = function (...o) {
                          let l = i.renderer.apply(this, o);
                          return l === !1 && (l = s.apply(this, o)), l;
                        })
                      : (t.renderers[i.name] = i.renderer);
                  }
                  if ("tokenizer" in i) {
                    if (
                      !i.level ||
                      (i.level !== "block" && i.level !== "inline")
                    )
                      throw new Error(
                        "extension level must be 'block' or 'inline'"
                      );
                    let s = t[i.level];
                    s ? s.unshift(i.tokenizer) : (t[i.level] = [i.tokenizer]),
                      i.start &&
                        (i.level === "block"
                          ? t.startBlock
                            ? t.startBlock.push(i.start)
                            : (t.startBlock = [i.start])
                          : i.level === "inline" &&
                            (t.startInline
                              ? t.startInline.push(i.start)
                              : (t.startInline = [i.start])));
                  }
                  "childTokens" in i &&
                    i.childTokens &&
                    (t.childTokens[i.name] = i.childTokens);
                }),
                (r.extensions = t)),
              n.renderer)
            ) {
              let i = this.defaults.renderer || new P(this.defaults);
              for (let s in n.renderer) {
                if (!(s in i))
                  throw new Error(`renderer '${s}' does not exist`);
                if (["options", "parser"].includes(s)) continue;
                let o = s,
                  l = n.renderer[o],
                  u = i[o];
                i[o] = (...p) => {
                  let c = l.apply(i, p);
                  return c === !1 && (c = u.apply(i, p)), c || "";
                };
              }
              r.renderer = i;
            }
            if (n.tokenizer) {
              let i = this.defaults.tokenizer || new y(this.defaults);
              for (let s in n.tokenizer) {
                if (!(s in i))
                  throw new Error(`tokenizer '${s}' does not exist`);
                if (["options", "rules", "lexer"].includes(s)) continue;
                let o = s,
                  l = n.tokenizer[o],
                  u = i[o];
                i[o] = (...p) => {
                  let c = l.apply(i, p);
                  return c === !1 && (c = u.apply(i, p)), c;
                };
              }
              r.tokenizer = i;
            }
            if (n.hooks) {
              let i = this.defaults.hooks || new $();
              for (let s in n.hooks) {
                if (!(s in i)) throw new Error(`hook '${s}' does not exist`);
                if (["options", "block"].includes(s)) continue;
                let o = s,
                  l = n.hooks[o],
                  u = i[o];
                $.passThroughHooks.has(s)
                  ? (i[o] = (p) => {
                      if (this.defaults.async)
                        return Promise.resolve(l.call(i, p)).then((f) =>
                          u.call(i, f)
                        );
                      let c = l.call(i, p);
                      return u.call(i, c);
                    })
                  : (i[o] = (...p) => {
                      let c = l.apply(i, p);
                      return c === !1 && (c = u.apply(i, p)), c;
                    });
              }
              r.hooks = i;
            }
            if (n.walkTokens) {
              let i = this.defaults.walkTokens,
                s = n.walkTokens;
              r.walkTokens = function (o) {
                let l = [];
                return (
                  l.push(s.call(this, o)),
                  i && (l = l.concat(i.call(this, o))),
                  l
                );
              };
            }
            this.defaults = { ...this.defaults, ...r };
          }),
          this
        );
      }
      setOptions(e) {
        return (this.defaults = { ...this.defaults, ...e }), this;
      }
      lexer(e, t) {
        return x.lex(e, t ?? this.defaults);
      }
      parser(e, t) {
        return b.parse(e, t ?? this.defaults);
      }
      parseMarkdown(e) {
        return (n, r) => {
          let i = { ...r },
            s = { ...this.defaults, ...i },
            o = this.onError(!!s.silent, !!s.async);
          if (this.defaults.async === !0 && i.async === !1)
            return o(
              new Error(
                "marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."
              )
            );
          if (typeof n > "u" || n === null)
            return o(
              new Error("marked(): input parameter is undefined or null")
            );
          if (typeof n != "string")
            return o(
              new Error(
                "marked(): input parameter is of type " +
                  Object.prototype.toString.call(n) +
                  ", string expected"
              )
            );
          s.hooks && ((s.hooks.options = s), (s.hooks.block = e));
          let l = s.hooks ? s.hooks.provideLexer() : e ? x.lex : x.lexInline,
            u = s.hooks ? s.hooks.provideParser() : e ? b.parse : b.parseInline;
          if (s.async)
            return Promise.resolve(s.hooks ? s.hooks.preprocess(n) : n)
              .then((p) => l(p, s))
              .then((p) => (s.hooks ? s.hooks.processAllTokens(p) : p))
              .then((p) =>
                s.walkTokens
                  ? Promise.all(this.walkTokens(p, s.walkTokens)).then(() => p)
                  : p
              )
              .then((p) => u(p, s))
              .then((p) => (s.hooks ? s.hooks.postprocess(p) : p))
              .catch(o);
          try {
            s.hooks && (n = s.hooks.preprocess(n));
            let p = l(n, s);
            s.hooks && (p = s.hooks.processAllTokens(p)),
              s.walkTokens && this.walkTokens(p, s.walkTokens);
            let c = u(p, s);
            return s.hooks && (c = s.hooks.postprocess(c)), c;
          } catch (p) {
            return o(p);
          }
        };
      }
      onError(e, t) {
        return (n) => {
          if (
            ((n.message += `
Please report this to https://github.com/markedjs/marked.`),
            e)
          ) {
            let r =
              "<p>An error occurred:</p><pre>" +
              w(n.message + "", !0) +
              "</pre>";
            return t ? Promise.resolve(r) : r;
          }
          if (t) return Promise.reject(n);
          throw n;
        };
      }
    };
    var L = new A();
    function d(a, e) {
      return L.parse(a, e);
    }
    d.options = d.setOptions = function (a) {
      return L.setOptions(a), (d.defaults = L.defaults), N(d.defaults), d;
    };
    d.getDefaults = _;
    d.defaults = O;
    d.use = function (...a) {
      return L.use(...a), (d.defaults = L.defaults), N(d.defaults), d;
    };
    d.walkTokens = function (a, e) {
      return L.walkTokens(a, e);
    };
    d.parseInline = L.parseInline;
    d.Parser = b;
    d.parser = b.parse;
    d.Renderer = P;
    d.TextRenderer = S;
    d.Lexer = x;
    d.lexer = x.lex;
    d.Tokenizer = y;
    d.Hooks = $;
    d.parse = d;
    var it = d.options,
      ot = d.setOptions,
      at = d.use,
      lt = d.walkTokens,
      ut = d.parseInline,
      pt = d,
      ct = b.parse,
      ht = x.lex;

    if (__exports != exports) module.exports = exports;
    return module.exports;
  }
);
//# sourceMappingURL=marked.umd.js.map
close;
